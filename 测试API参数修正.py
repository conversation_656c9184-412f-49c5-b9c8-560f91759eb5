#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试API参数修正
验证综合预警监测系统中的API参数是否正确配置
"""

from 综合预警监测系统 import ComprehensiveMonitoringSystem

def test_api_parameters():
    """测试API参数配置"""
    print("🧪 测试API参数修正")
    print("=" * 50)
    
    # 初始化系统
    system = ComprehensiveMonitoringSystem()
    
    # 测试区县
    test_counties = ['青田县', '鹿城区', '龙游县', '临安区']
    
    for county in test_counties:
        print(f"\n📍 测试区县: {county}")
        
        # 获取API参数
        params = system._get_water_api_params(county)
        city = system._get_city_from_county(county)
        
        print(f"   🏙️ 推断城市: {city}")
        print(f"   📡 API参数:")
        print(f"      - areaFlag: {params['areaFlag']}")
        print(f"      - sss: {params['sss']}")
        print(f"      - ssx: {params['ssx']}")
        print(f"      - zl: {params['zl']}")
        print(f"      - sklx: {params['sklx']}")
        print(f"      - sfcj: {params['sfcj']}")
        print(f"      - bxdj: {params['bxdj']}")
        
        # 验证参数完整性
        expected_zl = 'RR,ZZ,ZQ,DD,TT,'
        expected_sklx = '4,5,3,2,1,9,'
        expected_bxdj = '1,2,3,4,5,'
        
        if params['zl'] == expected_zl:
            print(f"   ✅ zl参数正确: {params['zl']}")
        else:
            print(f"   ❌ zl参数错误: 期望 {expected_zl}, 实际 {params['zl']}")
            
        if params['sklx'] == expected_sklx:
            print(f"   ✅ sklx参数正确: {params['sklx']}")
        else:
            print(f"   ❌ sklx参数错误: 期望 {expected_sklx}, 实际 {params['sklx']}")
            
        if params['bxdj'] == expected_bxdj:
            print(f"   ✅ bxdj参数正确: {params['bxdj']}")
        else:
            print(f"   ❌ bxdj参数错误: 期望 {expected_bxdj}, 实际 {params['bxdj']}")

def test_api_request_format():
    """测试API请求格式"""
    print(f"\n🌐 测试API请求格式")
    print("=" * 50)
    
    system = ComprehensiveMonitoringSystem()
    county = '青田县'
    params = system._get_water_api_params(county)
    
    # 构建完整的API URL
    base_url = "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater"
    
    # 构建查询字符串
    query_params = []
    for key, value in params.items():
        query_params.append(f"{key}={value}")
    
    query_string = "&".join(query_params)
    full_url = f"{base_url}?{query_string}"
    
    print(f"📡 完整API请求URL:")
    print(f"{full_url}")
    
    print(f"\n📋 参数解析:")
    for key, value in params.items():
        print(f"   {key} = {value}")
    
    # 验证关键参数
    print(f"\n🔍 关键参数验证:")
    print(f"   站点类别 (zl): {params['zl']}")
    print(f"     - RR: 水库 ✓")
    print(f"     - ZZ,ZQ: 河道 ✓") 
    print(f"     - DD: 堰闸 ✓")
    print(f"     - TT: 潮汐 ✓")
    
    print(f"   水库类型 (sklx): {params['sklx']}")
    print(f"     - 4,5: 大型水库 ✓")
    print(f"     - 3: 中型水库 ✓")
    print(f"     - 2: 小一型水库 ✓")
    print(f"     - 1,9: 其他水库 ✓")
    
    print(f"   报汛等级 (bxdj): {params['bxdj']}")
    print(f"     - 1,2,3,4,5: 所有等级 ✓")

def compare_old_vs_new():
    """对比修正前后的参数"""
    print(f"\n📊 对比修正前后的参数")
    print("=" * 50)
    
    county = '青田县'
    city = '丽水市'
    
    print(f"测试区县: {county} ({city})")
    
    # 修正前的参数（有问题的）
    old_params = {
        'areaFlag': '1',
        'sss': city,
        'ssx': county,
        'zl': 'RR,',           # ❌ 只有水库
        'sklx': '4,5,',        # ❌ 只有大型水库
        'sfcj': '1',
        'bxdj': '1,2,3,4,5,',  # ✅ 正确
    }
    
    # 修正后的参数（正确的）
    system = ComprehensiveMonitoringSystem()
    new_params = system._get_water_api_params(county)
    
    print(f"\n❌ 修正前 (有问题):")
    print(f"   zl = {old_params['zl']} (只包含水库)")
    print(f"   sklx = {old_params['sklx']} (只包含大型水库)")
    
    print(f"\n✅ 修正后 (正确):")
    print(f"   zl = {new_params['zl']} (包含所有站点类型)")
    print(f"   sklx = {new_params['sklx']} (包含所有水库类型)")
    
    print(f"\n📈 改进效果:")
    print(f"   - 站点类型覆盖: 1种 → 4种 (水库+河道+堰闸+潮汐)")
    print(f"   - 水库类型覆盖: 1种 → 4种 (大型+中型+小一型+其他)")
    print(f"   - 数据完整性: 大幅提升 🚀")

if __name__ == "__main__":
    print("🔧 API参数修正验证工具")
    print("=" * 60)
    
    try:
        # 测试API参数配置
        test_api_parameters()
        
        # 测试API请求格式
        test_api_request_format()
        
        # 对比修正前后
        compare_old_vs_new()
        
        print(f"\n✅ 所有测试完成！API参数修正验证通过。")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
