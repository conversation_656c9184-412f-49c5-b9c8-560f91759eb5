#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
综合预警监测系统 - 优化版
根据抓包分析和系统流程图重构
整合气象预警、雨情、水情数据的综合分析系统
"""

import requests
import json
import re
from datetime import datetime, timedelta
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class ComprehensiveMonitoringSystem:
    def __init__(self):
        """初始化综合监测系统"""
        
        # 系统配置 - 统一管理所有配置信息
        self.config = {
            # API配置
            'weather_base_url': "https://www.12379zj.cn",
            'rain_api_url': "https://sqfb.slt.zj.gov.cn/rest/newList/getNewTotalRainList",
            'water_api_url': "https://sqfb.slt.zj.gov.cn/rest/newList/getNewDataList",  # 根据抓包修正
            
            # 请求头配置
            'headers': {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Referer': 'https://sqfb.slt.zj.gov.cn/weIndex.html',
                'Accept-Language': 'zh-CN,zh;q=0.9'
            },
            
            # 预警筛选配置
            'filter_levels': ['Yellow'],  # 预警等级: Red, Orange, Yellow, Blue
            'filter_types': ['大风'],     # 预警类型
            
            # 水情API参数模板 - 根据抓包文件优化
            'water_params': {
                'areaFlag': '1',
                'sss': '',                    # 市级区域
                'ssx': '',                    # 县级区域
                'zl': 'RR,ZZ,ZQ,DD,TT,',     # 站点类型：水库+河道+堰闸+潮汐
                'sklx': '4,5,3,1,9,2,',      # 水库类型：大型+中型+小一型+其他
                'ly': '',
                'sfcj': '1',                 # 获取超限站点
                'bxdj': '1,2,3,4,5,',       # 报汛等级
                'zm': '',
                'cjly': '',
                'bx': '0'
            },
            
            # 站点类型映射
            'station_types': {
                'RR': '水库',
                'ZZ': '河道', 
                'ZQ': '河道',
                'DD': '堰闸',
                'TT': '潮汐'
            },
            
            # 水情状态映射
            'water_status': {
                'cx': '超限',
                'cjj': '超警戒', 
                'cbz': '超保证',
                'qt': '正常'
            }
        }

        print("🌦️ 综合预警监测系统初始化完成")
        print(f"✅ 已设置筛选等级: {self.config['filter_levels']}")
        print(f"✅ 已设置筛选类型: {self.config['filter_types']}")

    def _get_city_from_county(self, county):
        """根据区县获取对应的市级区域 - 统一城市映射"""
        city_mapping = {
            # 杭州市
            '上城区': '杭州市', '拱墅区': '杭州市', '西湖区': '杭州市', '滨江区': '杭州市',
            '萧山区': '杭州市', '余杭区': '杭州市', '临平区': '杭州市', '钱塘区': '杭州市',
            '富阳区': '杭州市', '临安区': '杭州市', '桐庐县': '杭州市', '淳安县': '杭州市', '建德市': '杭州市',
            
            # 宁波市
            '海曙区': '宁波市', '江北区': '宁波市', '北仑区': '宁波市', '镇海区': '宁波市',
            '鄞州区': '宁波市', '奉化区': '宁波市', '象山县': '宁波市', '宁海县': '宁波市',
            '余姚市': '宁波市', '慈溪市': '宁波市',
            
            # 温州市
            '鹿城区': '温州市', '龙湾区': '温州市', '瓯海区': '温州市', '洞头区': '温州市',
            '永嘉县': '温州市', '平阳县': '温州市', '苍南县': '温州市', '文成县': '温州市', '泰顺县': '温州市',
            '瑞安市': '温州市', '乐清市': '温州市', '龙港市': '温州市',
            
            # 台州市
            '椒江区': '台州市', '黄岩区': '台州市', '路桥区': '台州市', '三门县': '台州市',
            '天台县': '台州市', '仙居县': '台州市', '温岭市': '台州市', '临海市': '台州市', '玉环市': '台州市',
            
            # 其他地市...
            '嘉善县': '嘉兴市', '海盐县': '嘉兴市', '海宁市': '嘉兴市', '平湖市': '嘉兴市', '桐乡市': '嘉兴市',
            '德清县': '湖州市', '长兴县': '湖州市', '安吉县': '湖州市',
            '新昌县': '绍兴市', '诸暨市': '绍兴市', '嵊州市': '绍兴市',
            '武义县': '金华市', '浦江县': '金华市', '磐安县': '金华市', '兰溪市': '金华市', 
            '义乌市': '金华市', '东阳市': '金华市', '永康市': '金华市',
            '常山县': '衢州市', '开化县': '衢州市', '龙游县': '衢州市', '江山市': '衢州市',
            '岱山县': '舟山市', '嵊泗县': '舟山市',
            '青田县': '丽水市', '缙云县': '丽水市', '遂昌县': '丽水市', '松阳县': '丽水市',
            '云和县': '丽水市', '庆元县': '丽水市', '景宁县': '丽水市', '龙泉市': '丽水市'
        }
        return city_mapping.get(county, '浙江省')

    def get_weather_password(self):
        """获取气象预警API密码"""
        config_js_url = f"{self.config['weather_base_url']}/zjemw/resources/newClient/js/map/config/config.js"
        try:
            response = requests.get(config_js_url, timeout=10, verify=False)
            js_content = response.text
            password_pattern = r"'apiPassword'\s*:\s*'([^']+)'"
            match = re.search(password_pattern, js_content)
            
            if match:
                return match.group(1)
            else:
                # 备用密码
                return "CifcIqVPix/S4rwY6Y91ZIeAS9sw7DPQstphN8v5iqYQ8dfv6dolHImGoUih9vPr0WPyb1wTHCCcghKnzmIlIQDeytofz5ixJx9HlBYluDN9K3tf43gCNjSS4VEqpvk6RZ+DNvsjTcCNaEn9/KNR+foUkI51FBqjV96xxbcNGC8="
        except:
            return "CifcIqVPix/S4rwY6Y91ZIeAS9sw7DPQstphN8v5iqYQ8dfv6dolHImGoUih9vPr0WPyb1wTHCCcghKnzmIlIQDeytofz5ixJx9HlBYluDN9K3tf43gCNjSS4VEqpvk6RZ+DNvsjTcCNaEn9/KNR+foUkI51FBqjV96xxbcNGC8="

    def get_weather_warnings(self):
        """获取气象预警数据"""
        password = self.get_weather_password()
        api_url = f"{self.config['weather_base_url']}/api/share"
        
        data = {
            'userCode': 'ZJWechat',
            'apiPassword': password,
            'apiName': 'getWarningList',
            'apiParam': json.dumps({
                'pageSize': 100,
                'pageIndex': 1,
                'warningType': '',
                'warningLevel': '',
                'areaLevel': ''
            })
        }

        try:
            response = requests.post(api_url, data=data, timeout=30, verify=False)
            if response.status_code == 200:
                result = response.json()
                if result.get('returnCode') == '0':
                    return result.get('data', [])
            return []
        except Exception as e:
            print(f"❌ 获取气象预警失败: {e}")
            return []

    def get_rain_data(self, county, hours=24):
        """获取指定区县的雨情数据"""
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        params = {
            'areaFlag': '1',
            'st': start_time.strftime('%Y-%m-%dT%H:%M:%S'),
            'et': end_time.strftime('%Y-%m-%dT%H:%M:%S'),
            'max': '',
            'min': '0',
            'bool': 'true',
            'bxdj': '1,2,3,4,5,6',
            'type': '0'
        }

        try:
            response = requests.get(
                self.config['rain_api_url'],
                params=params,
                headers=self.config['headers'],
                timeout=30,
                verify=False
            )

            if response.status_code == 200:
                data = response.json()
                return {
                    'data': data,
                    'start_time': start_time.strftime('%Y-%m-%d %H:%M'),
                    'end_time': end_time.strftime('%Y-%m-%d %H:%M'),
                    'hours': hours
                }
            else:
                return {'data': [], 'start_time': '', 'end_time': '', 'hours': hours}
        except Exception as e:
            print(f"   ❌ 获取{county}雨情数据失败: {e}")
            return {'data': [], 'start_time': '', 'end_time': '', 'hours': hours}

    def get_water_data(self, county):
        """获取指定区县的水情数据 - 根据抓包文件优化"""
        city = self._get_city_from_county(county)
        
        # 根据抓包文件构建参数
        params = self.config['water_params'].copy()
        params['sss'] = city
        params['ssx'] = county
        
        print(f"   🌊 获取{county}水情数据...")

        try:
            response = requests.get(
                self.config['water_api_url'],
                params=params,
                headers=self.config['headers'],
                timeout=30,
                verify=False
            )

            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ 获取到水情JSON数据")

                # 根据抓包文件的响应结构解析数据
                all_stations = []
                type_counts = {}
                
                # 处理各种状态的站点
                for key, status_name in self.config['water_status'].items():
                    if key in data and data[key]:
                        stations = data[key]
                        type_counts[status_name] = len(stations)
                        all_stations.extend(stations)
                        print(f"   📊 {status_name}站点: {len(stations)}个")

                return all_stations, type_counts
            else:
                print(f"   ❌ 请求失败，状态码: {response.status_code}")
                return [], {}

        except Exception as e:
            print(f"   ❌ 获取{county}水情数据失败: {e}")
            return [], {}

    def format_water_summary(self, water_data, county="未知区县"):
        """格式化水情数据摘要"""
        if not water_data or len(water_data) != 2:
            return "无水情数据"

        stations, type_counts = water_data
        
        if not stations:
            return f"{county}: 无水情站点"
        
        # 统计站点类型
        station_type_counts = {}
        for station in stations:
            station_type = station.get('zl', 'RR')
            type_name = self.config['station_types'].get(station_type, '未知')
            station_type_counts[type_name] = station_type_counts.get(type_name, 0) + 1
        
        # 构建摘要
        summary_parts = []
        for status, count in type_counts.items():
            if count > 0:
                summary_parts.append(f"{status}{count}个")
        
        type_parts = []
        for type_name, count in station_type_counts.items():
            type_parts.append(f"{type_name}{count}个")
        
        summary = f"{county}: {', '.join(summary_parts)}"
        if type_parts:
            summary += f" ({', '.join(type_parts)})"
        
        return summary

    def run_comprehensive_monitoring(self):
        """运行综合监测 - 根据系统流程图实现"""
        print("\n🌦️ 开始综合预警监测...")

        # 1. 获取气象预警
        print("\n📡 获取气象预警数据...")
        warnings = self.get_weather_warnings()
        print(f"✅ 获取到 {len(warnings)} 条预警信息")

        # 2. 筛选预警并提取区县
        target_counties = set()
        if warnings:
            print("\n🔍 分析预警覆盖区域...")
            for warning in warnings[:5]:  # 处理前5条预警
                title = warning.get('title', '')
                sender = warning.get('sender', '')

                # 简单的区县提取逻辑
                for county in ['台州市', '椒江区', '温岭市', '临海市', '黄岩区', '路桥区']:
                    if county in title or county in sender:
                        target_counties.add(county)
                        print(f"   � 发现目标区县: {county}")

        # 如果没有从预警中提取到区县，使用默认测试区县
        if not target_counties:
            target_counties = {'台州市', '椒江区', '温岭市'}
            print("   📍 使用默认测试区县")

        # 3. 获取各区县的雨情和水情数据
        print(f"\n📊 获取 {len(target_counties)} 个区县的雨情和水情数据...")

        for county in sorted(target_counties):
            print(f"\n🔍 处理 {county}...")

            # 获取雨情数据
            print(f"   🌧️ 获取{county}雨情数据...")
            rain_data = self.get_rain_data(county, hours=24)
            rain_count = len(rain_data.get('data', []))
            print(f"   ✅ 雨情站点: {rain_count}个")

            # 获取水情数据
            water_data = self.get_water_data(county)
            water_summary = self.format_water_summary(water_data, county)
            print(f"   🌊 {water_summary}")

            # 显示详细水情信息
            if water_data and len(water_data) == 2:
                stations, type_counts = water_data
                if stations:
                    for i, station in enumerate(stations[:2]):  # 显示前2个站点
                        station_name = station.get('zm', '未知站点')
                        station_type = station.get('zl', 'RR')
                        type_name = self.config['station_types'].get(station_type, '未知')
                        print(f"     - {station_name} ({type_name})")

        print("\n✅ 综合监测完成")

if __name__ == "__main__":
    try:
        print("🌦️ 启动综合预警监测系统...")
        system = ComprehensiveMonitoringSystem()

        # 运行综合监测
        system.run_comprehensive_monitoring()

    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()

    print("\n🏁 程序结束")
