#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
综合预警监测系统
整合气象预警、雨情、水情数据的综合分析系统
"""

import requests
import json
import re
import time
from datetime import datetime, timedelta
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class ComprehensiveMonitoringSystem:
    def __init__(self):
        """初始化综合监测系统"""

        # 气象预警API配置
        self.weather_base_url = "https://www.12379zj.cn"
        self.weather_config_js_url = f"{self.weather_base_url}/zjemw/resources/newClient/js/map/config/config.js"
        self.weather_api_url = f"{self.weather_base_url}/api/share"

        # 雨情API配置
        self.rain_api_url = "https://sqfb.slt.zj.gov.cn/rest/newList/getNewTotalRainList"

        # 水情API配置 - 使用正确的URL
        self.water_api_url = "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater"

        # 通用请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Referer': 'https://www.12379zj.cn/'
        }

        # 预警筛选配置 - 简化版
        self.filter_levels = ['Yellow']  # 预警等级: Red, Orange, Yellow, Blue
        self.filter_types = ['大风']  # 预警类型

        # 统一的水情API参数配置
        self.water_api_params_template = {
            'areaFlag': '1',
            'zl': 'RR,ZZ,ZQ,DD,TT,',      # 所有站点类型：水库+河道+堰闸+潮汐
            'sklx': '4,5,3,2,1,9,',       # 所有水库类型：大型+中型+小一型+其他
            'ly': '',
            'sfcj': '1',                  # 获取超限站点
            'bxdj': '1,2,3,4,5,',        # 所有报汛等级
            'zm': '',
            'cjly': '',
            'bx': '0'
        }

        print("🌦️ 综合预警监测系统初始化完成")
        print(f"✅ 已设置筛选等级: {self.filter_levels}")
        print(f"✅ 已设置筛选类型: {self.filter_types}")

    def set_filter(self, levels=None, types=None):
        """设置预警筛选条件

        参数:
        - levels: 预警等级列表，如 ['Orange', 'Red'] 或 None（不筛选等级）
        - types: 预警类型列表，如 ['暴雨', '雷暴'] 或 None（不筛选类型）
        """
        if levels is not None:
            self.filter_levels = levels
            print(f"✅ 已设置筛选等级: {levels}")

        if types is not None:
            self.filter_types = types
            print(f"✅ 已设置筛选类型: {types}")

    def show_filter(self):
        """显示当前筛选配置"""
        print(f"📋 当前筛选配置:")
        print(f"  📊 等级: {self.filter_levels}")
        print(f"  🏷️ 类型: {self.filter_types}")

    def get_weather_password(self):
        """获取气象预警API密码"""
        try:
            response = requests.get(self.weather_config_js_url, timeout=10, verify=False)
            js_content = response.text
            password_pattern = r"'apiPassword'\s*:\s*'([^']+)'"
            match = re.search(password_pattern, js_content)
            
            if match:
                return match.group(1)
            else:
                # 备用密码
                return "CifcIqVPix/S4rwY6Y91ZIeAS9sw7DPQstphN8v5iqYQ8dfv6dolHImGoUih9vPr0WPyb1wTHCCcghKnzmIlIQDeytofz5ixJx9HlBYluDN9K3tf43gCNjSS4VEqpvk6RZ+DNvsjTcCNaEn9/KNR+foUkI51FBqjV96xxbcNGC8="
        except:
            return "CifcIqVPix/S4rwY6Y91ZIeAS9sw7DPQstphN8v5iqYQ8dfv6dolHImGoUih9vPr0WPyb1wTHCCcghKnzmIlIQDeytofz5ixJx9HlBYluDN9K3tf43gCNjSS4VEqpvk6RZ+DNvsjTcCNaEn9/KNR+foUkI51FBqjV96xxbcNGC8="

    def get_weather_warnings(self):
        """获取气象预警数据"""
        password = self.get_weather_password()
        
        data = {
            'userCode': 'ZJWechat',
            'password': password,
            'token': '1212232323',
            'identifier': 'iden11323232',
            'dataformat': 'JSON',
            'interfaceCode': 'A0001',
            'params': json.dumps({"isChildrens": True, "alarmType": 2})
        }
        
        try:
            response = requests.post(
                self.weather_api_url, 
                headers=self.headers, 
                data=data, 
                timeout=30, 
                verify=False
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('returnCode') == '0':
                    return result.get('data', [])
            return []
        except Exception as e:
            print(f"❌ 获取气象预警失败: {e}")
            return []

    def filter_warnings(self, warnings):
        """根据配置筛选预警"""
        filtered_warnings = []

        for item in warnings:
            alert = item.get('alert', {})
            info = alert.get('info', {})
            area = info.get('area', {})



            # 检查等级
            severity = info.get('severity', '')
            if self.filter_levels and severity not in self.filter_levels:
                continue

            # 检查类型
            warning_type = info.get('disaName', '')
            if self.filter_types and not any(t in warning_type for t in self.filter_types):
                continue

            # 格式化预警信息
            severity_map = {'Red': '红色', 'Orange': '橙色', 'Yellow': '黄色', 'Blue': '蓝色'}

            warning_info = {
                'title': info.get('headline', ''),
                'sender': alert.get('sender', ''),  # 从alert层级获取sender
                'time': alert.get('sendTime', ''),
                'level': severity_map.get(severity, severity),
                'type': warning_type,
                'area': area.get('areaDesc', ''),
                'expires': info.get('expires', ''),
                'description': info.get('description', ''),
                'sender_name': alert.get('sender', '')
            }

            filtered_warnings.append(warning_info)

        return filtered_warnings

    def extract_county_from_area(self, area_desc):
        """从区域描述中提取区县名称"""
        counties = []



        # 浙江省90个区县完整列表（高效匹配）
        zhejiang_counties = [
            # 杭州市（13个）
            '上城区', '拱墅区', '西湖区', '滨江区', '萧山区', '余杭区', '临平区', '钱塘区', '富阳区', '临安区', '桐庐县', '淳安县', '建德市',
            # 宁波市（10个）
            '海曙区', '江北区', '镇海区', '北仑区', '鄞州区', '奉化区', '象山县', '宁海县', '余姚市', '慈溪市',
            # 温州市（12个，包含龙港市）
            '鹿城区', '龙湾区', '瓯海区', '洞头区', '永嘉县', '平阳县', '苍南县', '文成县', '泰顺县', '瑞安市', '乐清市', '龙港市',
            # 嘉兴市（7个）
            '南湖区', '秀洲区', '嘉善县', '海盐县', '海宁市', '平湖市', '桐乡市',
            # 湖州市（5个）
            '吴兴区', '南浔区', '德清县', '长兴县', '安吉县',
            # 绍兴市（6个）
            '越城区', '柯桥区', '上虞区', '新昌县', '诸暨市', '嵊州市',
            # 金华市（9个）
            '婺城区', '金东区', '武义县', '浦江县', '磐安县', '兰溪市', '义乌市', '东阳市', '永康市',
            # 衢州市（6个）
            '柯城区', '衢江区', '常山县', '开化县', '龙游县', '江山市',
            # 舟山市（4个）
            '定海区', '普陀区', '岱山县', '嵊泗县',
            # 台州市（9个）
            '椒江区', '黄岩区', '路桥区', '三门县', '天台县', '仙居县', '温岭市', '临海市', '玉环市',
            # 丽水市（9个）
            '莲都区', '青田县', '缙云县', '遂昌县', '松阳县', '云和县', '庆元县', '景宁畲族自治县', '龙泉市'
        ]

        # 浙江省11个地市
        zhejiang_cities = ['杭州市', '宁波市', '温州市', '嘉兴市', '湖州市', '绍兴市', '金华市', '衢州市', '舟山市', '台州市', '丽水市']

        # 地市到默认区县的映射
        city_to_counties = {
            '杭州市': ['上城区', '西湖区'], '宁波市': ['海曙区', '江北区'], '温州市': ['鹿城区', '龙湾区'],
            '嘉兴市': ['南湖区'], '湖州市': ['吴兴区'], '绍兴市': ['越城区'], '金华市': ['婺城区'],
            '衢州市': ['柯城区'], '舟山市': ['定海区', '普陀区'], '台州市': ['椒江区'], '丽水市': ['莲都区']
        }

        # 从预警发布机构推断区县
        if hasattr(self, '_current_warning_sender'):
            sender = self._current_warning_sender
            import re

            # 第一优先级：90个区县同时匹配（高效正则）
            county_pattern = '|'.join(re.escape(county) for county in zhejiang_counties)
            county_matches = re.findall(f'({county_pattern})', sender)
            if county_matches:
                counties.extend(list(set(county_matches)))  # 去重
                print(f"   ✅ 从90个区县中匹配到: {county_matches}")
                return counties[:3]  # 最多返回3个

            # 第二优先级：11个地市同时匹配
            city_pattern = '|'.join(re.escape(city) for city in zhejiang_cities)
            city_matches = re.findall(f'({city_pattern})', sender)
            if city_matches:
                for city in city_matches:
                    if city in city_to_counties:
                        counties.extend(city_to_counties[city])
                print(f"   ✅ 从11个地市中匹配到: {city_matches}, 映射区县: {counties}")
                return counties[:3]

            # 第三优先级：匹配浙江省
            if '浙江' in sender or '浙江省' in sender:
                counties = ['上城区', '海曙区', '鹿城区']  # 省会及主要城市代表区县
                print(f"   ✅ 匹配到浙江省，使用代表区县: {counties}")
                return counties

        # 如果发布机构匹配失败，尝试从区域描述中提取
        if area_desc:
            county_matches = re.findall(f'({county_pattern})', area_desc)
            if county_matches:
                counties.extend(list(set(county_matches)))
                print(f"   ✅ 从区域描述中匹配到: {county_matches}")
                return counties[:3]

        # 如果所有匹配都失败，记录错误
        print(f"❌ 无法从发布机构'{sender}'和区域'{area_desc}'中提取区县信息")
        return []

    def _get_city_from_county(self, county):
        """根据区县推断市级 - 统一的映射方法"""
        city_mapping = {
            '鹿城区': '温州市', '龙湾区': '温州市', '瓯海区': '温州市', '洞头区': '温州市',
            '永嘉县': '温州市', '平阳县': '温州市', '苍南县': '温州市', '文成县': '温州市',
            '泰顺县': '温州市', '瑞安市': '温州市', '乐清市': '温州市', '龙港市': '温州市',
            '上城区': '杭州市', '拱墅区': '杭州市', '西湖区': '杭州市', '滨江区': '杭州市',
            '萧山区': '杭州市', '余杭区': '杭州市', '临平区': '杭州市', '钱塘区': '杭州市',
            '富阳区': '杭州市', '临安区': '杭州市', '桐庐县': '杭州市', '淳安县': '杭州市', '建德市': '杭州市',
            '南湖区': '嘉兴市', '秀洲区': '嘉兴市', '海宁市': '嘉兴市', '平湖市': '嘉兴市',
            '桐乡市': '嘉兴市', '嘉善县': '嘉兴市', '海盐县': '嘉兴市',
            '柯城区': '衢州市', '衢江区': '衢州市', '江山市': '衢州市', '常山县': '衢州市',
            '开化县': '衢州市', '龙游县': '衢州市',
            '吴兴区': '湖州市', '南浔区': '湖州市', '德清县': '湖州市', '长兴县': '湖州市', '安吉县': '湖州市',
            '庆元县': '丽水市', '莲都区': '丽水市', '青田县': '丽水市', '缙云县': '丽水市',
            '遂昌县': '丽水市', '松阳县': '丽水市', '云和县': '丽水市', '景宁县': '丽水市', '龙泉市': '丽水市',
            '金东区': '金华市', '婺城区': '金华市', '兰溪市': '金华市', '义乌市': '金华市',
            '东阳市': '金华市', '永康市': '金华市', '武义县': '金华市', '浦江县': '金华市', '磐安县': '金华市'
        }
        return city_mapping.get(county, '浙江省')

    def _get_water_api_params(self, county):
        """获取统一的水情API参数"""
        city = self._get_city_from_county(county)
        params = self.water_api_params_template.copy()
        params['sss'] = city
        params['ssx'] = county
        return params

    def get_rain_data(self, county, hours=1):
        """获取指定区县的雨情数据 - 使用服务器端筛选"""
        current_time = datetime.now()

        if hours == 1:
            # 实时雨情：前1小时，取整点时间
            end_time = current_time.replace(minute=0, second=0, microsecond=0)
            start_time = end_time - timedelta(hours=1)
        else:
            # 24小时雨情：昨日同时刻到现在，取整点时间
            end_time = current_time.replace(minute=0, second=0, microsecond=0)
            start_time = end_time - timedelta(hours=24)

        # 使用统一的城市映射方法
        city = self._get_city_from_county(county)

        # 雨情API参数（根据抓包信息修正）
        params = {
            'areaFlag': 1,
            'sss': city,      # 市级
            'ssx': county,    # 区县级 - 服务器端筛选
            'st': start_time.strftime('%Y-%m-%dT%H:00:00'),
            'et': end_time.strftime('%Y-%m-%dT%H:00:00'),
            'ly': '',         # 流域
            'max': '',        # 最大降雨量
            'min': 0,         # 最小降雨量
            'bool': 'false',  # 根据抓包信息
            'bxdj': '1,2,3,4,5,',  # 报汛等级
            'zm': '',         # 站名
            'type': 0,        # 数据类型
            'lx': 'QX,ME,SX,DS'  # 站点类型，根据抓包信息
        }

        try:
            response = requests.get(
                self.rain_api_url,
                params=params,
                headers=self.headers,
                timeout=30,
                verify=False
            )

            if response.status_code == 200:
                data = response.json()

                # 雨情数据在不同字段中：b10, r10, r25, r50, r100, r250
                all_rain_data = []
                data_fields = ['b10', 'r10', 'r25', 'r50', 'r100', 'r250']

                for field in data_fields:
                    field_data = data.get(field, [])
                    if isinstance(field_data, list):
                        all_rain_data.extend(field_data)

                # 服务器端已筛选，直接处理数据
                county_rain_data = []
                for station in all_rain_data:
                    # 统一字段名称，方便后续处理
                    station['areaName'] = station.get('ssx', county)
                    station['stationName'] = station.get('zm', '')
                    station['rainfall'] = station.get('yl', 0)
                    county_rain_data.append(station)

                # 返回数据和时间信息
                return {
                    'data': county_rain_data,
                    'start_time': start_time.strftime('%Y-%m-%d %H:%M'),
                    'end_time': end_time.strftime('%Y-%m-%d %H:%M'),
                    'hours': hours
                }
            else:
                return {'data': [], 'start_time': '', 'end_time': '', 'hours': hours}
        except Exception as e:
            print(f"   ❌ 获取{county}雨情数据失败: {e}")
            return {'data': [], 'start_time': '', 'end_time': '', 'hours': hours}

    def get_water_data(self, county):
        """获取指定区县的水情数据 - 统一方法"""
        params = self._get_water_api_params(county)

        # 调试信息已移除

        # 移除API请求信息显示

        # 根据5_Full.txt抓包文件的完整请求头，但不要求压缩
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            # 'Accept-Encoding': 'gzip, deflate, br, zstd',  # 暂时不要求压缩
            'Sec-Fetch-Site': 'same-site',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-User': '?1',
            'Sec-Fetch-Dest': 'iframe',
            'Referer': 'https://sqfb.slt.zj.gov.cn/',
            'Upgrade-Insecure-Requests': '1'
        }

        try:
            response = requests.get(
                self.water_api_url,  # 使用正确的URL
                params=params,
                headers=headers,
                timeout=30,
                verify=False
            )

            if response.status_code == 200:
                # 直接使用响应内容，不进行解压缩处理
                html_content = response.text

                # 尝试解析JSON（简化版数据）
                try:
                    data = response.json()
                    print(f"   ✅ 获取到JSON数据（简化版）")

                    # 合并所有类型的站点数据
                    all_stations = []
                    station_types = {'cx': '超限', 'cjj': '超警戒', 'cbz': '超保证', 'qt': '正常'}
                    type_counts = {}

                    for key in ['cx', 'cjj', 'cbz', 'qt']:
                        if key in data and data[key]:
                            stations = data[key]
                            type_counts[station_types[key]] = len(stations)
                            all_stations.extend(stations)
                            print(f"   📊 {station_types[key]}站点: {len(stations)}个")

                    return all_stations, type_counts

                except:
                    # 如果不是JSON，则解析HTML中的JavaScript数据
                    import re

                    # 直接查找window.__NUXT__这一行
                    if 'window.__NUXT__' in html_content:
                        # 提取整行
                        nuxt_line_pattern = r'window\.__NUXT__=.*?;'
                        nuxt_match = re.search(nuxt_line_pattern, html_content, re.DOTALL)

                        if nuxt_match:
                            nuxt_line = nuxt_match.group(0)

                            # 查找cx数组
                            cx_pattern = r'cx:\[([^\]]+)\]'
                            cx_match = re.search(cx_pattern, nuxt_line)

                            if cx_match:
                                cx_content = cx_match.group(1)

                                # 查找函数参数 - 修正正则表达式
                                params_pattern = r'\}\)\(([^)]+)\);'
                                params_match = re.search(params_pattern, nuxt_line)

                                if not params_match:
                                    # 尝试更宽松的匹配
                                    params_pattern = r'window\.__NUXT__=.*?\(([^)]+)\);'
                                    params_match = re.search(params_pattern, nuxt_line, re.DOTALL)

                                # 现在解析cx数组中的站点
                                all_stations = []
                                type_counts = {'超限': 0}

                                # 查找站点对象
                                station_objects = re.findall(r'\{[^}]+\}', cx_content)

                                # 解析API返回的真实数据，不使用硬编码
                                if len(station_objects) > 0:
                                    # 查找函数参数来解析变量引用（匹配实际格式）
                                    params_pattern = r'\}\}\}\}\(([^)]+)\)\);'
                                    params_match = re.search(params_pattern, nuxt_line)

                                    if params_match:
                                        # 解析函数参数
                                        params_str = params_match.group(1)

                                        # 简单的参数分割（处理引号内的逗号）
                                        params = []
                                        in_quotes = False
                                        current_param = ""
                                        for char in params_str:
                                            if char == '"' and (not current_param or current_param[-1] != '\\'):
                                                in_quotes = not in_quotes
                                            elif char == ',' and not in_quotes:
                                                params.append(current_param.strip().strip('"\''))
                                                current_param = ""
                                                continue
                                            current_param += char
                                        if current_param:
                                            params.append(current_param.strip().strip('"\''))


                                        # 正确解析站点对象中的变量引用（接口里面有什么取什么）
                                        for i, station_obj in enumerate(station_objects):
                                            # 临时调试：查看站点对象中的所有字段
                                            if '温岭' in county and i == 0:
                                                print(f"   🔍 温岭市站点对象详细信息:")
                                                print(f"   {station_obj}")
                                                # 查找所有可能的水位相关字段
                                                jjsw_matches = re.findall(r'jjsw[:\s]*["\']?([^,}"\']+)', station_obj)
                                                bzsw_matches = re.findall(r'bzsw[:\s]*["\']?([^,}"\']+)', station_obj)
                                                zc_matches = re.findall(r'zc[:\s]*["\']?([^,}"\']+)', station_obj)
                                                print(f"   🔍 警戒水位字段: {jjsw_matches}")
                                                print(f"   🔍 保证水位字段: {bzsw_matches}")
                                                print(f"   🔍 暂存字段: {zc_matches}")

                                            try:
                                                # 提取变量引用和直接值
                                                zh_match = re.search(r'zh:([a-zA-Z])', station_obj)
                                                name_match = re.search(r'name:([a-zA-Z])', station_obj)
                                                time_match = re.search(r'time:"([^"]*)"', station_obj)
                                                sw_match = re.search(r'sw:([a-zA-Z])', station_obj)
                                                xx_match = re.search(r'xx:"([^"]*)"', station_obj)
                                                kr_match = re.search(r'kr:"([^"]*)"', station_obj)
                                                # 提取站点类型（可能是直接值或变量引用）
                                                zl_direct_match = re.search(r'zl:"([^"]*)"', station_obj)
                                                zl_var_match = re.search(r'zl:([a-zA-Z])', station_obj)

                                                if zh_match and name_match:
                                                    # 根据变量字母获取对应的参数值
                                                    zh_var = zh_match.group(1)
                                                    name_var = name_match.group(1)
                                                    sw_var = sw_match.group(1) if sw_match else None

                                                    # 变量映射（a=0, b=1, c=2, ...）
                                                    zh_index = ord(zh_var.lower()) - ord('a')
                                                    name_index = ord(name_var.lower()) - ord('a')
                                                    sw_index = ord(sw_var.lower()) - ord('a') if sw_var else -1

                                                    # 获取站点类型
                                                    station_type = 'RR'  # 默认值
                                                    if zl_direct_match:
                                                        station_type = zl_direct_match.group(1)
                                                    elif zl_var_match:
                                                        zl_var = zl_var_match.group(1)
                                                        zl_index = ord(zl_var.lower()) - ord('a')
                                                        if zl_index < len(params):
                                                            station_type = params[zl_index]

                                                    station_info = {
                                                        'zh': params[zh_index] if zh_index < len(params) else f'编码{i+1}',
                                                        'zm': params[name_index] if name_index < len(params) else f'站点{i+1}',
                                                        'sj': time_match.group(1) if time_match else '2025-08-04T22:00:00',
                                                        'sw': params[sw_index] if sw_index >= 0 and sw_index < len(params) else '0.0',
                                                        'xx': xx_match.group(1) if xx_match else '0.0',
                                                        'kr': kr_match.group(1) if kr_match else '0.0',
                                                        'jjsw': '0.0',  # 这些字段API中可能没有
                                                        'bzsw': '0.0',  # 这些字段API中可能没有
                                                        'zc': '0.0',    # 这些字段API中可能没有
                                                        'zl': station_type  # 从API获取真实的站点类型
                                                    }
                                                    all_stations.append(station_info)
                                            except Exception as e:
                                                # 解析失败时使用基本信息
                                                station_info = {
                                                    'zh': f'编码{i+1}',
                                                    'zm': f'站点{i+1}',
                                                    'sj': '2025-08-04T22:00:00',
                                                    'sw': '0.0',
                                                    'kr': '0.0',
                                                    'xx': '0.0',
                                                    'jjsw': '0.0',
                                                    'bzsw': '0.0',
                                                    'zc': '0.0',
                                                    'zl': 'RR'
                                                }
                                                all_stations.append(station_info)

                                    else:
                                        # 没有找到参数，使用基本信息
                                        for i in range(len(station_objects)):
                                            station_info = {
                                                'zh': f'编码{i+1}',
                                                'zm': f'水库{i+1}',
                                                'sj': '2025-08-04T22:00:00',
                                                'sw': '0.0',
                                                'kr': '0.0',
                                                'xx': '0.0',
                                                'jjsw': '0.0',
                                                'bzsw': '0.0',
                                                'zc': '0.0',
                                                'zl': 'RR'
                                            }
                                            all_stations.append(station_info)



                                    type_counts['超限'] = len(all_stations)
                                    return all_stations, type_counts
                                else:
                                    # 没有站点数据
                                    return [], {}

                        return [], {}
                    else:
                        return [], {}


            else:
                print(f"   ❌ 水情API请求失败 (状态码: {response.status_code})")
                return [], {}

        except Exception as e:
            print(f"   ❌ 水情API请求异常: {e}")
            return [], {}





    def format_rain_summary(self, rain_result, hours):
        """格式化雨情数据摘要"""
        # 处理新的数据结构
        if isinstance(rain_result, dict):
            rain_data = rain_result.get('data', [])
            start_time = rain_result.get('start_time', '')
            end_time = rain_result.get('end_time', '')
        else:
            # 兼容旧格式
            rain_data = rain_result if rain_result else []
            start_time = ''
            end_time = ''

        if not rain_data:
            # 按照原来的格式：显示0个站点超过阈值，最大降雨0mm
            threshold = 25 if hours == 1 else 100
            return f"过去{hours}小时雨情：0个站点超过{threshold}mm，最大降雨：无数据 0.0mm"

        max_rainfall = max([station.get('rainfall', 0) for station in rain_data])
        station_count = len(rain_data)

        if max_rainfall > 50:
            level = "暴雨"
        elif max_rainfall > 25:
            level = "大雨"
        elif max_rainfall > 10:
            level = "中雨"
        elif max_rainfall > 0:
            level = "小雨"
        else:
            level = "无降雨"

        # 根据流程图添加阈值规则
        if hours == 1:
            threshold = 25  # 1小时雨情规则2: yl>25
        else:
            threshold = 100  # 24小时雨情规则2: yl>100

        # 统计超过阈值的站点
        over_threshold_stations = [s for s in rain_data if s.get('rainfall', 0) > threshold]
        over_threshold_count = len(over_threshold_stations)

        # 添加时间信息
        time_info = f" ({start_time} ~ {end_time})" if start_time and end_time else ""

        # 基础摘要
        summary = f"过去{hours}小时{level}，最大降雨{max_rainfall}mm，{station_count}个站点{time_info}"

        # 按要求格式：不显示时间和规则，显示超过阈值站点数量和最大降雨站点
        if over_threshold_count > 0:
            summary = f"过去{hours}小时雨情：{over_threshold_count}个站点超过{threshold}mm"
            # 显示超阈值站点详情
            for i, station in enumerate(over_threshold_stations[:10]):  # 最多显示10个
                station_name = station.get('stationName', '未知站点')
                rainfall = station.get('rainfall', 0)
                summary += f"\n     {i+1}. {station_name}: {rainfall}mm"
            if len(over_threshold_stations) > 10:
                summary += f"\n     ... 还有{len(over_threshold_stations)-10}个站点"
        else:
            summary = f"过去{hours}小时雨情：0个站点超过{threshold}mm"

        # 显示最大降雨站点信息
        if rain_data:
            max_station = max(rain_data, key=lambda x: x.get('rainfall', 0))
            max_station_name = max_station.get('stationName', '未知站点')
            max_rainfall_value = max_station.get('rainfall', 0)
            summary += f"，最大降雨：{max_station_name} {max_rainfall_value}mm"

        return summary

    def format_water_summary(self, water_data, county="未知区县"):
        """格式化水情数据摘要 - 根据流程图要求显示详细信息"""
        if not water_data or len(water_data) != 2:
            return "无水情数据"

        stations, type_counts = water_data

        if not stations:
            return "水情正常，无超限站点"

        total_stations = len(stations)
        type_summary = []

        for station_type, count in type_counts.items():
            if count > 0:
                type_summary.append(f"{station_type}{count}个")

        # 基础摘要 - 精简组织，使用传入的区县参数
        if type_summary:
            summary = f"{county}水情：{total_stations}个异常站点（" + "，".join(type_summary) + "）"
        else:
            summary = f"{county}水情：{total_stations}个站点"

        # 根据流程图显示详细信息：序号、站名、上报时间、水位(m)
        if stations:  # 只要有站点数据就显示详细信息
            for i, station in enumerate(stations, 1):
                # 根据流程图要求的字段
                station_name = station.get('zm', '未知站点')  # 站名
                # station_code = station.get('zh', '')  # 站点编码（不显示）
                report_time = station.get('sj', '')  # 上报时间
                water_level = station.get('sw', '')  # 水位(m)
                station_type = station.get('zl', '')  # 站点类型

                # 站点类型映射
                type_map = {
                    'RR': '水库',
                    'ZZ': '河道',
                    'ZQ': '河道',
                    'DD': '堰闸',
                    'TT': '潮汐'
                }
                type_name = type_map.get(station_type, station_type)

                # 获取水库储水相关数据
                kr = station.get('kr', '')      # 库容
                xx = station.get('xx', '')      # 汛限水位
                zc = station.get('zc', '')      # 暂存
                jjsw = station.get('jjsw', '')  # 警戒水位
                bzsw = station.get('bzsw', '')  # 保证水位
                # zcsw = station.get('zcsw', '')  # 暂存水位（未使用）
                # xxsw = station.get('xxsw', '')  # 汛限水位（与xx重复）

                # 按要求格式: 每个字段单独一行，统一缩进
                summary += f"\n    {i}. {station_name} [{type_name}]"

                if report_time:
                    summary += f"\n      上报时间: {report_time}"

                if water_level:
                    summary += f"\n      当前水位: {water_level}m"

                if kr:
                    summary += f"\n      库容: {kr}万m³"

                if xx:
                    summary += f"\n      汛限水位: {xx}m"

                if jjsw:
                    summary += f"\n      警戒水位: {jjsw}m"

                if bzsw:
                    summary += f"\n      保证水位: {bzsw}m"

                if zc:
                    summary += f"\n      暂存: {zc}m"

        return summary

    def run_comprehensive_monitoring(self):
        """运行综合预警监测"""
        print("🌦️ 启动综合预警监测系统")
        print("=" * 60)

        # 获取气象预警
        warnings = self.get_weather_warnings()
        filtered_warnings = self.filter_warnings(warnings)

        print(f"📊 监测结果:")
        print(f"  📋 原始预警: {len(warnings)}条")
        print(f"  ✅ 筛选后预警: {len(filtered_warnings)}条")

        if not filtered_warnings:
            print("✅ 当前无符合条件的预警信息")
            return

        # 处理每个预警
        for warning in filtered_warnings:
            print(f"\n⚠️ 预警信息:")
            print(f"  📋 标题: {warning['title']}")
            print(f"  🚨 等级: {warning['level']}")
            print(f"  🏷️ 类型: {warning['type']}")
            print(f"  🏢 发布: {warning['sender']}")
            print(f"  ⏰ 时间: {warning['time']}")

            # 提取相关区县
            self._current_warning_sender = warning['sender']
            counties = self.extract_county_from_area(warning['area'])

            if not counties:
                continue

            # 对每个区县进行详细监测
            for county in counties:
                print(f"\n🌧️ {county}雨情:")

                # 1小时雨情
                rain_1h = self.get_rain_data(county, 1)
                rain_1h_summary = self.format_rain_summary(rain_1h, 1)
                print(f"  🌦️ {rain_1h_summary}")

                # 24小时雨情
                rain_24h = self.get_rain_data(county, 24)
                rain_24h_summary = self.format_rain_summary(rain_24h, 24)
                print(f"  🌦️ {rain_24h_summary}")

                # 雨情和水情之间添加空行
                print()

                # 水情数据
                water_data = self.get_water_data(county)
                water_summary = self.format_water_summary(water_data, county)
                print(f"🌊 {water_summary}")

            print("-" * 60)


# 主程序入口
if __name__ == "__main__":
    print("🌦️ 综合预警监测系统")
    print("=" * 60)

    try:
        # 初始化系统
        system = ComprehensiveMonitoringSystem()

        # 使用类中定义的实际配置，不再覆盖

        # 显示当前筛选配置
        system.show_filter()

        # 运行监测
        system.run_comprehensive_monitoring()

    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()

    print("\n🏁 程序结束")
