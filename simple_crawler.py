#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
12379预警信息爬虫 - 优化版
使用API参数进行服务端筛选，支持warningType、warningLevel、areaLevel筛选
显示所有字段，只保存CSV
"""

import requests
import csv
import time
from datetime import datetime
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class WarningCrawler:
    def __init__(self, limit, max_pages, warning_type='', warning_level='', area_level=''):
        self.limit = limit
        self.max_pages = max_pages

        # 参数映射字典
        self.warning_level_map = {
            '蓝色': 'Blue',
            '黄色': 'Yellow',
            '橙色': 'Orange',
            '红色': 'Red',
            'blue': 'Blue',
            'yellow': 'Yellow',
            'orange': 'Orange',
            'red': 'Red'
        }

        self.warning_type_map = {
            # 基于实际数据的完整映射表
            '暴雨': '11B03',
            '寒潮': '11B05',
            '大风': '11B06',
            '高温': '11B09',
            '雷电': '11B14',
            '冰雹': '11B15',
            '霜冻': '11B16',
            '大雾': '11B17',
            '道路结冰': '11B21',
            '森林火险': '11B25',
            '地质灾害气象风险': '11B37',
            '低温': '11B56',
            '雷暴大风': '11B58',
            '城市内涝气象风险': '11B75',
            '森林火险气象风险': '11B77',
            '城市暴雨积涝': '11B94',
            '台风': '11B01',
            '洪水': '11A01',
            '山洪灾害': '11A51',
            '农业干旱': '11A52',
            '山洪灾害气象风险': '11A56',
            '海啸事件': '11E01',
            '风暴潮': '11E02',
            '海浪': '11E06',
            '农业气象灾害': '11F15',
            '森林火险预警': '11G09',
            '风险': '11Y06',
            '人防警报信息': '14Y05',
            '警报试鸣信息': '14Y06'
        }

        self.area_level_map = {
            '省级': '1',
            '市级': '3',  # 修正：3是市级
            '县级': '4',  # 修正：4是县级
            '其他': '2'   # 修正：2是其他
        }

        # 转换用户输入的参数
        self.warning_type = self._convert_warning_type(warning_type)
        self.warning_level = self._convert_warning_level(warning_level)
        self.area_level = self._convert_area_level(area_level)

        # API配置
        self.base_url = "https://www.12379zj.cn"
        self.api_url = f"{self.base_url}/zjemw/warning/warningList"

        # 设置请求配置
        self._setup_request_config()

    def _convert_warning_type(self, warning_type):
        """转换预警类型参数"""
        if not warning_type:
            return ''
        # 如果已经是API代码，直接返回
        if warning_type.startswith('11B'):
            return warning_type
        # 否则查找映射
        return self.warning_type_map.get(warning_type, warning_type)

    def _convert_warning_level(self, warning_level):
        """转换预警等级参数"""
        if not warning_level:
            return ''
        # 如果已经是API代码，直接返回
        if warning_level in ['Blue', 'Yellow', 'Orange', 'Red']:
            return warning_level
        # 否则查找映射
        return self.warning_level_map.get(warning_level, warning_level)

    def _convert_area_level(self, area_level):
        """转换区域级别参数"""
        if not area_level:
            return ''
        # 如果已经是API代码，直接返回
        if area_level in ['1', '2', '3', '4']:
            return area_level
        # 否则查找映射
        return self.area_level_map.get(area_level, area_level)

    def get_available_options(self):
        """获取所有可用的筛选选项"""
        return {
            'warning_types': list(self.warning_type_map.keys()),
            'warning_levels': list(self.warning_level_map.keys()),
            'area_levels': list(self.area_level_map.keys())
        }

    def _setup_request_config(self):
        """设置请求配置"""
        # 请求头
        self.headers = {
            'Host': 'www.12379zj.cn',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'X-Requested-With': 'XMLHttpRequest',
            'Origin': 'https://www.12379zj.cn',
            'Referer': 'https://www.12379zj.cn/zjemw/warning/warningList.html',
            'Connection': 'keep-alive'
        }

        # 查询参数 (使用API筛选)
        self.default_params = {
            'textcontex': '',
            'page': 1,
            'limit': self.limit,
            'warningLevel': self.warning_level,  # 使用API筛选
            'warningType': self.warning_type,    # 使用API筛选
            'areaLevel': self.area_level,        # 使用API筛选
            'isEffect': '',
            'curYear': 1,
            'alarmType': 2
        }

    def get_page_data(self, page=1):
        """获取指定页面的预警数据"""
        params = self.default_params.copy()
        params['page'] = page
        
        try:
            print(f"正在获取第 {page} 页数据 (每页 {self.limit} 条)...")
            
            response = requests.post(
                self.api_url,
                headers=self.headers,
                data=params,
                timeout=30,
                verify=False
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 0:
                    result_data = data.get('data', [])
                    total_count = data.get('count', 0)

                    # 修复显示-1的问题：如果总数无效，显示为"未知"
                    display_total = total_count if total_count > 0 else "未知"
                    print(f"第 {page} 页获取成功，本页 {len(result_data)} 条，总计 {display_total} 条")
                    return {
                        'success': True,
                        'data': result_data,
                        'total_count': total_count
                    }
                else:
                    print(f"第 {page} 页API返回错误: {data.get('message', '未知错误')}")
                    return {'success': False, 'error': data.get('message', '未知错误')}
            else:
                print(f"第 {page} 页HTTP请求失败: {response.status_code}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            print(f"第 {page} 页请求异常: {e}")
            return {'success': False, 'error': str(e)}

    def get_all_pages(self):
        """获取所有页面的预警数据"""
        all_warnings = []
        page = 1
        total_count_saved = 0  # 保存第一页的总数

        print("开始获取预警数据...")

        # 显示筛选条件
        filters = self.get_filter_info()
        if filters:
            print(f"API筛选条件: {', '.join(filters)}")
        else:
            print("无筛选条件，获取所有数据")

        while True:
            # 检查是否达到最大页数限制
            if self.max_pages and page > self.max_pages:
                print(f"已达到最大页数限制 ({self.max_pages} 页)，停止获取")
                break

            result = self.get_page_data(page)

            if not result['success']:
                print(f"获取第 {page} 页失败，停止获取")
                break

            page_data = result['data']
            if not page_data:
                print(f"第 {page} 页无数据，获取完成")
                break

            # 直接使用API筛选后的数据，无需本地筛选
            all_warnings.extend(page_data)
            total_count = result['total_count']

            # 保存第一页的总数，后续页面使用保存的值
            if page == 1:
                total_count_saved = total_count
            else:
                # 如果后续页面返回-1或无效值，使用第一页保存的总数
                if total_count <= 0:
                    total_count = total_count_saved

            # 计算显示的页数信息
            total_pages = (total_count + self.limit - 1) // self.limit
            actual_max_pages = self.max_pages if self.max_pages else total_pages
            if self.max_pages:
                print(f"进度: {page}/{actual_max_pages} 页 (共{total_pages}页可用)，已获取 {len(all_warnings)} 条")
            else:
                print(f"进度: {page}/{total_pages} 页，已获取 {len(all_warnings)} 条")

            # 如果当前页数据少于限制数量，说明是最后一页
            if len(page_data) < self.limit:
                print("已获取所有数据")
                break

            page += 1

            # 添加延迟避免请求过快
            time.sleep(0.5)
        
        print(f"获取完成！总计 {len(all_warnings)} 条预警数据")
        return all_warnings

    def get_filter_info(self):
        """获取当前筛选条件的信息，用于显示"""
        filters = []
        if self.warning_type:
            filters.append(f"预警类型='{self.warning_type}'")
        if self.warning_level:
            filters.append(f"预警等级='{self.warning_level}'")
        if self.area_level:
            filters.append(f"区域级别='{self.area_level}'")
        return filters

    def save_to_csv(self, warnings, filename=None):
        """保存预警数据到CSV文件 - 包含所有字段"""
        if not warnings:
            print("没有预警数据可保存")
            return None
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"12379_warnings_{timestamp}.csv"
        
        try:
            # 获取所有字段名
            all_fields = set()
            for warning in warnings:
                all_fields.update(warning.keys())
            
            # 按字母顺序排序字段
            fieldnames = sorted(all_fields)
            
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(warnings)
            
            print(f"CSV文件已保存: {filename}")
            print(f"包含字段: {', '.join(fieldnames)}")
            return filename
            
        except Exception as e:
            print(f"保存CSV文件失败: {e}")
            return None

    def print_summary(self, warnings):
        """打印数据摘要"""
        if not warnings:
            print("没有预警数据")
            return

        print(f"\n数据摘要:")
        print(f"总计: {len(warnings)} 条预警")

        # 统计预警类型
        type_count = {}
        level_count = {}
        sender_count = {}

        for warning in warnings:
            # 预警类型统计
            warning_type = warning.get('disaName', '未知')
            type_count[warning_type] = type_count.get(warning_type, 0) + 1

            # 预警等级统计
            level = warning.get('severity', '未知')
            level_count[level] = level_count.get(level, 0) + 1

            # 发布机构统计
            sender = warning.get('sender', '未知')
            sender_count[sender] = sender_count.get(sender, 0) + 1

        print(f"\n预警类型分布 (用于WARNING_TYPE参数):")
        for wtype, count in sorted(type_count.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  '{wtype}': {count}条")

        # 显示预警类型和对应的eventType代码映射
        print(f"\n预警类型与API代码映射 (用于完善映射表):")
        type_code_map = {}
        for warning in warnings:
            warning_type = warning.get('disaName', '未知')
            event_type = warning.get('eventType', '未知')
            if warning_type != '未知' and event_type != '未知':
                type_code_map[warning_type] = event_type

        for wtype, code in sorted(type_code_map.items()):
            print(f"  '{wtype}' → '{code}'")

        print(f"\n预警等级分布 (用于WARNING_LEVEL参数):")
        for level, count in sorted(level_count.items(), key=lambda x: x[1], reverse=True):
            print(f"  '{level}': {count}条")

        print(f"\n发布机构分布 (前10名, 用于AREA_LEVEL参数):")
        for sender, count in sorted(sender_count.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  '{sender}': {count}条")

        # 分析发布机构级别
        area_levels = {}
        for sender in sender_count.keys():
            if '省' in sender:
                level = '省级'
            elif '市' in sender:
                level = '市级'
            elif '县' in sender:
                level = '县级'
            else:
                level = '其他'
            area_levels[level] = area_levels.get(level, 0) + sender_count[sender]

        print(f"\n发布机构级别分布:")
        for level, count in sorted(area_levels.items(), key=lambda x: x[1], reverse=True):
            print(f"  {level}: {count}条")

def main():
    """主函数"""
    print("12379预警信息爬虫启动")
    print("=" * 60)

    # ========== 配置区域 - 在这里修改参数 ==========
    LIMIT = 100           # 每页数量
    MAX_PAGES = 1          # 最大页数

    # 筛选条件 (空字符串表示不筛选) - 支持中文名称，自动转换为API代码
    WARNING_TYPE = ''      # 预警类型筛选 (如: '暴雨', '雷电', '大风', '台风' 等中文名称)
    WARNING_LEVEL = ''     # 预警等级筛选 (如: '蓝色', '黄色', '橙色', '红色' 等中文名称)
    AREA_LEVEL = ''        # 区域级别筛选 (如: '省级', '市级', '县级', '其他' 等中文名称)

    # 使用说明:
    # 1. 现在支持中文参数，如 WARNING_TYPE = '暴雨', WARNING_LEVEL = '蓝色'
    # 2. 也支持原API代码，如 WARNING_TYPE = '11B03', WARNING_LEVEL = 'Blue'
    # 3. 可以同时使用多个筛选条件
    # 4. 筛选使用API参数，高效获取符合条件的数据
    # 5. 支持的中文参数见下方示例

    # 支持的中文参数:
    # 预警类型: '暴雨', '雷电', '大风', '雷暴大风', '大雾', '霜冻', '高温', '台风', '道路结冰', '寒潮', '地质灾害气象风险', '山洪灾害', '森林火险气象风险', '海浪', '风暴潮', '洪水'
    # 预警等级: '蓝色', '黄色', '橙色', '红色' (也支持英文: 'Blue', 'Yellow', 'Orange', 'Red')
    # 区域级别: '省级', '市级', '县级', '其他'
    # ============================================

    # 创建爬虫实例
    crawler = WarningCrawler(
        limit=LIMIT,
        max_pages=MAX_PAGES,
        warning_type=WARNING_TYPE,
        warning_level=WARNING_LEVEL,
        area_level=AREA_LEVEL
    )
    
    # 获取数据
    raw_warnings = crawler.get_all_pages()
    
    if raw_warnings:
        # 打印摘要
        crawler.print_summary(raw_warnings)
        
        # 保存CSV文件
        csv_file = crawler.save_to_csv(raw_warnings)
        
        print("\n" + "=" * 60)
        print("爬取完成！")
        print(f"总计获取 {len(raw_warnings)} 条预警信息")
        if csv_file:
            print(f"数据已保存为: {csv_file}")
        print("=" * 60)
    else:
        print("未获取到任何数据")

if __name__ == "__main__":
    main()
