#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def get_html_content():
    """直接获取HTML内容"""
    
    # 真实API
    url = "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater"
    
    # 测试永嘉县参数
    params = {
        'areaFlag': '1',
        'sss': '温州市',
        'ssx': '永嘉县',
        'zl': 'RR,',
        'sklx': '4,5,',
        'ly': '',
        'sfcj': '1',
        'bxdj': '1,2,3,4,5,',
        'zm': '',
        'cjly': '',
        'bx': '0'
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Referer': 'https://sqfb.slt.zj.gov.cn/',
        'Accept-Language': 'zh-CN,zh;q=0.9'
    }
    
    print("🔍 获取HTML内容...")
    print(f"URL: {url}")
    print(f"参数: {params}")
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=30, verify=False)
        
        print(f"状态码: {response.status_code}")
        print(f"内容类型: {response.headers.get('Content-Type', 'unknown')}")
        print(f"内容长度: {len(response.text)}")
        
        if response.status_code == 200:
            # 保存HTML内容
            filename = "水情页面_永嘉县.html"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"✅ HTML内容已保存到 '{filename}'")
            
            # 显示HTML的前1000字符
            print(f"\nHTML内容预览:")
            print("="*60)
            print(response.text[:1000])
            print("="*60)
            
            # 简单分析
            print(f"\n简单分析:")
            print(f"- 包含'水库': {'水库' in response.text}")
            print(f"- 包含'邵川': {'邵川' in response.text}")
            print(f"- 包含'超限': {'超限' in response.text}")
            print(f"- 包含'station': {'station' in response.text.lower()}")
            print(f"- 包含'data': {'data' in response.text.lower()}")
            print(f"- 包含'json': {'json' in response.text.lower()}")
            
            return True
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

if __name__ == "__main__":
    print("🎯 直接获取HTML内容工具")
    success = get_html_content()
    
    if success:
        print("\n✅ 成功获取HTML内容")
        print("💡 请查看生成的HTML文件进行进一步分析")
    else:
        print("\n❌ 获取HTML内容失败")
