#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import urllib3
from datetime import datetime, timedelta

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def debug_rain_api():
    """调试雨情API，模拟实时雨情.py的调用"""
    
    # 完全按照实时雨情.py的方式
    end_time = datetime.now().strftime('%Y-%m-%dT%H:00:00')
    start_dt = datetime.now() - timedelta(hours=24)
    start_time = start_dt.strftime('%Y-%m-%dT%H:00:00')
    
    print(f"时间范围: {start_time} 到 {end_time}")
    
    # 完全按照实时雨情.py的参数
    params = {
        'areaFlag': 1,
        'st': start_time,
        'et': end_time,
        'min': 0,
        'bool': 'true',
        'bxdj': '1,2,3,4,5,6',
        'type': 0
    }
    params['max'] = ''
    
    print(f"API参数: {params}")
    
    base_url = "https://sqfb.slt.zj.gov.cn/rest/newList/getNewTotalRainList"
    
    try:
        response = requests.get(base_url, params=params, timeout=30, verify=False)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据结构: {list(data.keys())}")
            
            # 检查各个数据字段
            for key in data.keys():
                if isinstance(data[key], list):
                    print(f"{key}: {len(data[key])}条数据")
                    if data[key] and len(data[key]) > 0:
                        print(f"  第一条数据: {data[key][0]}")
                else:
                    print(f"{key}: {data[key]}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    debug_rain_api()
