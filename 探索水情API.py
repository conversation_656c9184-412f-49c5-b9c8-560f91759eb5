#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_water_apis():
    """测试不同的水情API接口"""
    
    base_url = "https://sqfb.slt.zj.gov.cn"
    
    # 可能的API路径
    api_paths = [
        "/rest/newList/getNewDataList",  # 当前使用的
        "/rest/realtime/getWaterData",   # 实时水情
        "/rest/water/getRealTimeData",   # 实时数据
        "/rest/map/getWaterStations",    # 水情站点
        "/rest/data/getWaterLevel",      # 水位数据
        "/rest/monitor/getWaterInfo",    # 水情监测
        "/api/water/realtime",           # 实时水情API
        "/api/data/water",               # 水情数据API
        "/rest/newList/getWaterDetail",  # 水情详情
        "/rest/station/getWaterData"     # 站点水情数据
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Referer': 'https://sqfb.slt.zj.gov.cn/'
    }
    
    # 基本参数
    params = {
        'areaFlag': '1',
        'sss': '温州市',
        'ssx': '永嘉县',
        'zl': 'RR,',  # 只查水库
        'sfcj': '1'   # 超限
    }
    
    for api_path in api_paths:
        url = base_url + api_path
        print(f"\n测试API: {api_path}")
        
        try:
            response = requests.get(url, params=params, headers=headers, timeout=10, verify=False)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"返回数据类型: {type(data)}")
                    if isinstance(data, dict):
                        print(f"数据字段: {list(data.keys())}")
                        # 查看是否有水位相关字段
                        for key, value in data.items():
                            if isinstance(value, list) and value:
                                first_item = value[0]
                                if isinstance(first_item, dict):
                                    item_keys = list(first_item.keys())
                                    print(f"  {key}字段包含: {item_keys}")
                                    # 检查是否有水位、时间等字段
                                    water_fields = [k for k in item_keys if any(w in k.lower() for w in ['water', 'level', 'time', 'tm', 'sj', 'sw', 'z'])]
                                    if water_fields:
                                        print(f"    可能的水位/时间字段: {water_fields}")
                                break
                    elif isinstance(data, list) and data:
                        first_item = data[0]
                        if isinstance(first_item, dict):
                            print(f"列表项字段: {list(first_item.keys())}")
                except json.JSONDecodeError:
                    print(f"非JSON响应: {response.text[:100]}...")
            else:
                print(f"请求失败: {response.text[:100]}...")
                
        except Exception as e:
            print(f"请求异常: {e}")

def test_detailed_params():
    """测试更详细的参数组合"""
    
    url = "https://sqfb.slt.zj.gov.cn/rest/newList/getNewDataList"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Referer': 'https://sqfb.slt.zj.gov.cn/'
    }
    
    # 尝试不同的参数组合
    param_sets = [
        # 基本参数
        {
            'areaFlag': '1',
            'sss': '温州市',
            'ssx': '永嘉县',
            'zl': 'RR,',
            'sfcj': '1'
        },
        # 添加更多参数
        {
            'areaFlag': '1',
            'sss': '温州市',
            'ssx': '永嘉县',
            'zl': 'RR,',
            'sfcj': '1',
            'detail': '1',  # 详细信息
            'includeLevel': '1'  # 包含水位
        },
        # 尝试不同的数据类型
        {
            'areaFlag': '1',
            'sss': '温州市',
            'ssx': '永嘉县',
            'zl': 'RR,',
            'sfcj': '0',  # 所有站点
            'dataType': 'detail'
        }
    ]
    
    for i, params in enumerate(param_sets, 1):
        print(f"\n参数组合 {i}: {params}")
        
        try:
            response = requests.get(url, params=params, headers=headers, timeout=10, verify=False)
            
            if response.status_code == 200:
                data = response.json()
                print(f"返回数据结构: {list(data.keys())}")
                
                # 检查每个字段的详细内容
                for key in ['cx', 'cjj', 'cbz', 'qt']:
                    if key in data and data[key]:
                        stations = data[key]
                        print(f"{key}: {len(stations)}个站点")
                        if stations:
                            first_station = stations[0]
                            print(f"  字段: {list(first_station.keys())}")
                            # 显示所有字段的值
                            for field, value in first_station.items():
                                print(f"    {field}: {value}")
                            break
            else:
                print(f"请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"请求异常: {e}")

if __name__ == "__main__":
    print("=== 测试不同的API路径 ===")
    test_water_apis()
    
    print("\n\n=== 测试详细参数 ===")
    test_detailed_params()
