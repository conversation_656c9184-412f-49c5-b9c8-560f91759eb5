#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
精确水情解析器
专门用于精确解析5_Full.txt中的__NUXT__数据
"""

import json
import re
from datetime import datetime

class PreciseWaterParser:
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
    def parse_from_file(self, file_path):
        """
        从文件中解析__NUXT__数据
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            return self.parse_nuxt_data(html_content)
            
        except Exception as e:
            print(f"读取文件失败: {e}")
            return None
    
    def parse_nuxt_data(self, html_content):
        """
        解析HTML中的__NUXT__数据
        """
        try:
            # 查找__NUXT__数据
            start_marker = "window.__NUXT__="
            end_marker = ";</script>"
            
            start_index = html_content.find(start_marker)
            if start_index == -1:
                print("未找到__NUXT__数据")
                return None
                
            start_index += len(start_marker)
            end_index = html_content.find(end_marker, start_index)
            
            if end_index == -1:
                print("未找到__NUXT__数据结束标记")
                return None
            
            # 提取JavaScript代码
            js_code = html_content[start_index:end_index]
            print(f"提取的JavaScript代码长度: {len(js_code)}")
            print(f"JavaScript代码开头: {js_code[:200]}")
            print(f"JavaScript代码结尾: {js_code[-200:]}")

            # 解析JavaScript函数调用
            return self.parse_js_code(js_code)
            
        except Exception as e:
            print(f"解析__NUXT__数据失败: {e}")
            return None
    
    def parse_js_code(self, js_code):
        """
        解析JavaScript代码
        """
        try:
            # 提取函数参数
            # 模式: (function(a,b,c,...){...})(param1,param2,...)
            # 从结尾找到最后的参数列表
            pattern = r'\}\}\}\(([^)]+)\)$'
            match = re.search(pattern, js_code)

            if not match:
                print("未找到JavaScript函数调用模式")
                print("尝试直接提取最后的参数...")
                # 直接从代码中提取最后的参数部分
                # 找到最后一个 }}} 后面的 (参数)
                last_brace_pos = js_code.rfind('}}}')
                if last_brace_pos != -1:
                    remaining = js_code[last_brace_pos + 3:]
                    # 提取 (参数) 部分
                    if remaining.startswith('(') and remaining.endswith(')'):
                        params_str = remaining[1:-1]  # 去掉括号
                        print(f"直接提取的参数: {params_str}")
                        params = self.parse_parameters(params_str)
                        print(f"解析到 {len(params)} 个参数")
                        return self.build_water_data(params)
                return None
            
            params_str = match.group(1)
            print(f"找到参数字符串: {params_str}")
            
            # 解析参数
            params = self.parse_parameters(params_str)
            print(f"解析到 {len(params)} 个参数")
            
            # 构建水情数据
            return self.build_water_data(params)
            
        except Exception as e:
            print(f"解析JavaScript代码失败: {e}")
            return None
    
    def parse_parameters(self, params_str):
        """
        解析函数参数 - 使用更简单的方法
        """
        try:
            # 使用正则表达式来分割参数
            import re

            # 匹配参数：null, "字符串", 数字
            pattern = r'(null|"[^"]*"|\d+\.?\d*)'
            matches = re.findall(pattern, params_str)

            params = []
            for match in matches:
                params.append(self.convert_param(match))

            print(f"正则匹配到的参数: {matches[:10]}...")
            return params

        except Exception as e:
            print(f"解析参数失败: {e}")
            return []
    
    def convert_param(self, param):
        """
        转换参数类型
        """
        param = param.strip()
        
        if param == 'null':
            return None
        elif param == 'true':
            return True
        elif param == 'false':
            return False
        elif param.startswith('"') and param.endswith('"'):
            return param[1:-1]  # 去掉引号
        elif param.startswith("'") and param.endswith("'"):
            return param[1:-1]  # 去掉引号
        else:
            # 尝试转换为数字
            try:
                # 检查是否是纯数字（不包含字母、冒号、连字符等）
                if param.replace('.', '').replace('-', '').isdigit() and not ':' in param and not '-' in param[1:]:
                    if '.' in param:
                        return float(param)
                    else:
                        return int(param)
                else:
                    return param
            except ValueError:
                return param
    
    def build_water_data(self, params):
        """
        根据解析的参数构建水情数据
        """
        try:
            # 基于实际的参数顺序构建数据
            # (null,"-","临安区","桐庐县","63000600","08-04T17:20:00","青山水库","0.0","23.87",119.796944,30.250833,"RR","1","浙江省","杭州市","4","2025-08-04T17:20:00","70101500","富春江电站","23.21",119.652498,29.707602)

            print(f"参数列表: {params[:10]}...")  # 显示前10个参数

            if len(params) < 22:
                print(f"参数数量不足: {len(params)}")
                return None
            
            # 构建水库数据
            reservoirs = []
            
            # 青山水库
            reservoir1 = {
                "index": 1,
                "zh": str(params[4]),  # "63000600"
                "city": "杭-临安",
                "county": str(params[2]),  # "临安区"
                "time": str(params[5]),  # "08-04T17:20:00"
                "name": str(params[6]),  # "青山水库"
                "jj": str(params[1]),  # "-"
                "bz": str(params[1]),  # "-"
                "zc": str(params[7]),  # "0.0"
                "xx": "23.16",
                "kr": "42.16",
                "sw": str(params[8]),  # "23.87"
                "zcsw": str(params[1]),  # "-"
                "lon": params[9] if isinstance(params[9], (int, float)) else float(params[9]),  # 119.796944
                "lat": params[10] if isinstance(params[10], (int, float)) else float(params[10]),  # 30.250833
                "info": {
                    "zh": str(params[4]),
                    "zm": str(params[6]),
                    "ly": params[0],  # null
                    "ITEM": "PZQE",
                    "class": " B Q",
                    "xzqhm": "330112",
                    "wd": params[10] if isinstance(params[10], (int, float)) else float(params[10]),
                    "jd": params[9] if isinstance(params[9], (int, float)) else float(params[9]),
                    "zl": str(params[11]),  # "RR"
                    "cjly": str(params[12]),  # "1"
                    "pro": str(params[13]),  # "浙江省"
                    "sss": str(params[14]),  # "杭州市"
                    "ssx": str(params[2]),  # "临安区"
                    "zcsw": params[0],  # null
                    "sklx": str(params[15]),  # "4"
                    "bdgc": params[0],  # null
                    "kr": 42.16,
                    "zc": 0,
                    "sbsj": str(params[16]),  # "2025-08-04T17:20:00"
                    "jjsw": params[0],  # null
                    "bzsw": params[0],  # null
                    "ztgc": params[0],  # null
                    "ytgc": params[0],  # null
                    "OBHTZ": params[0],  # null
                    "OBHTZTM": params[0],  # null
                    "xxsw": 23.16,
                    "sw": str(params[8])  # "23.87"
                },
                "counties": str(params[2])  # "临安区"
            }
            reservoirs.append(reservoir1)
            
            # 富春江电站
            reservoir2 = {
                "index": 2,
                "zh": str(params[17]),  # "70101500"
                "city": "杭-桐庐",
                "county": str(params[3]),  # "桐庐县"
                "time": str(params[16]),  # "2025-08-04T17:20:00"
                "name": str(params[18]),  # "富春江电站"
                "jj": str(params[1]),  # "-"
                "bz": str(params[1]),  # "-"
                "zc": str(params[7]),  # "0.0"
                "xx": "23.00",
                "kr": "454.34",
                "sw": str(params[19]),  # "23.21"
                "zcsw": str(params[1]),  # "-"
                "lon": params[20] if isinstance(params[20], (int, float)) else float(params[20]),  # 119.652498
                "lat": params[21] if isinstance(params[21], (int, float)) else float(params[21]),  # 29.707602
                "info": {
                    "zh": str(params[17]),
                    "zm": str(params[18]),
                    "ly": "钱塘江水系",
                    "ITEM": "PZQ",
                    "class": " B  ",
                    "xzqhm": "330122",
                    "wd": params[21] if isinstance(params[21], (int, float)) else float(params[21]),
                    "jd": params[20] if isinstance(params[20], (int, float)) else float(params[20]),
                    "zl": str(params[11]),  # "RR"
                    "cjly": str(params[12]),  # "1"
                    "pro": str(params[13]),  # "浙江省"
                    "sss": str(params[14]),  # "杭州市"
                    "ssx": str(params[3]),  # "桐庐县"
                    "zcsw": params[0],  # null
                    "sklx": str(params[15]),  # "4"
                    "bdgc": params[0],  # null
                    "kr": 454.34,
                    "zc": 0.04,
                    "sbsj": str(params[16]),  # "2025-08-04T17:20:00"
                    "jjsw": params[0],  # null
                    "bzsw": params[0],  # null
                    "ztgc": params[0],  # null
                    "ytgc": params[0],  # null
                    "OBHTZ": params[0],  # null
                    "OBHTZTM": params[0],  # null
                    "xxsw": 23,
                    "sw": str(params[19])  # "23.21"
                },
                "counties": str(params[3])  # "桐庐县"
            }
            reservoirs.append(reservoir2)
            
            # 构建完整的数据结构
            water_data = {
                "layout": "default",
                "data": [{
                    "waterData": {
                        "cjj": [],
                        "qt": [],
                        "cx": reservoirs,
                        "cbz": []
                    },
                    "ThData": "3.88(2025-08-04 17:00:00)"
                }],
                "fetch": {},
                "error": params[0],  # null
                "serverRendered": True,
                "routePath": "/new/realtimeWater",
                "config": {
                    "_app": {
                        "basePath": "/nuxtsyq/",
                        "assetsPath": "/nuxtsyq/_nuxt/",
                        "cdnURL": params[0]  # null
                    }
                },
                "timestamp": self.timestamp,
                "parsed_params": params
            }
            
            return water_data
            
        except Exception as e:
            print(f"构建水情数据失败: {e}")
            return None
    
    def format_data(self, data):
        """
        格式化数据为易读格式
        """
        if not data or "data" not in data:
            return None
            
        try:
            water_data = data["data"][0]["waterData"]
            
            formatted = {
                "解析时间": data.get("timestamp", ""),
                "水库监测点": [],
                "潮汐数据": data["data"][0].get("ThData", ""),
                "统计信息": {
                    "水库数量": len(water_data.get("cx", [])),
                    "潮汐监测点数量": len(water_data.get("cjj", [])),
                    "其他监测点数量": len(water_data.get("qt", []))
                },
                "解析参数": data.get("parsed_params", [])
            }
            
            # 处理水库数据
            for reservoir in water_data.get("cx", []):
                reservoir_info = {
                    "序号": reservoir.get("index"),
                    "站号": reservoir.get("zh"),
                    "名称": reservoir.get("name"),
                    "所属区域": f"{reservoir.get('city')}-{reservoir.get('county')}",
                    "当前水位(m)": reservoir.get("sw"),
                    "汛限水位(m)": reservoir.get("xx"),
                    "库容(万m³)": reservoir.get("kr"),
                    "经纬度": f"{reservoir.get('lon')}, {reservoir.get('lat')}",
                    "上报时间": reservoir.get("time"),
                    "流域": reservoir.get("info", {}).get("ly", ""),
                    "水库类型": reservoir.get("info", {}).get("sklx", "")
                }
                formatted["水库监测点"].append(reservoir_info)
            
            return formatted
            
        except Exception as e:
            print(f"格式化数据失败: {e}")
            return None
    
    def save_data(self, data, filename):
        """
        保存数据到文件
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"数据已保存到: {filename}")
        except Exception as e:
            print(f"保存文件失败: {e}")

# 使用示例
if __name__ == "__main__":
    parser = PreciseWaterParser()
    
    print("🌊 开始解析5_Full.txt中的水情数据...")
    data = parser.parse_from_file("5_Full.txt")
    
    if data:
        print("✅ 数据解析成功!")
        
        # 格式化数据
        formatted = parser.format_data(data)
        
        if formatted:
            # 保存数据
            parser.save_data(data, "完整水情数据.json")
            parser.save_data(formatted, "格式化水情数据_精确版.json")
            
            # 显示结果
            print(f"\n=== 解析结果 ===")
            print(f"解析时间: {formatted['解析时间']}")
            print(f"水库数量: {formatted['统计信息']['水库数量']}")
            print(f"潮汐数据: {formatted['潮汐数据']}")
            
            print(f"\n=== 水库详情 ===")
            for reservoir in formatted['水库监测点']:
                print(f"• {reservoir['名称']} ({reservoir['所属区域']})")
                print(f"  站号: {reservoir['站号']}")
                print(f"  当前水位: {reservoir['当前水位(m)']}m")
                print(f"  汛限水位: {reservoir['汛限水位(m)']}m")
                print(f"  库容: {reservoir['库容(万m³)']}万m³")
                print(f"  经纬度: {reservoir['经纬度']}")
                print(f"  上报时间: {reservoir['上报时间']}")
                print(f"  流域: {reservoir['流域'] or '未知'}")
                print()
            
            print(f"解析的参数数量: {len(formatted['解析参数'])}")
            print("前10个参数:", formatted['解析参数'][:10])
    else:
        print("❌ 数据解析失败!")
