#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
水情爬虫配置示例
演示如何自定义配置参数
"""

from accurate_water_crawler import WaterCrawlerConfig, AccurateWaterCrawler

# ============================================
# 配置示例1: 监测浙江省超限水库
# ============================================

class ZhejiangExceedConfig(WaterCrawlerConfig):
    """浙江省超限水库监测配置"""
    
    DEFAULT_QUERY = {
        'area_flag': '行政区划',
        'city': '浙江省',
        'district': '',
        'station_categories': ['水库'],
        'reservoir_types': ['所有水库'],
        'exceed_limit': '超限',  # 仅超限站点
        'alert_levels': ['3', '4', '5']  # 仅高风险等级
    }

# ============================================
# 配置示例2: 监测宁波市河道潮汐
# ============================================

class NingboRiverTideConfig(WaterCrawlerConfig):
    """宁波市河道潮汐监测配置"""
    
    DEFAULT_QUERY = {
        'area_flag': '行政区划',
        'city': '宁波市',
        'district': '',
        'station_categories': ['河道', '潮汐'],
        'reservoir_types': ['所有水库'],
        'exceed_limit': '所有',
        'alert_levels': ['1', '2', '3', '4', '5']
    }

# ============================================
# 配置示例3: 监测大型水库
# ============================================

class LargeReservoirConfig(WaterCrawlerConfig):
    """大型水库监测配置"""
    
    DEFAULT_QUERY = {
        'area_flag': '行政区划',
        'city': '浙江省',
        'district': '',
        'station_categories': ['水库'],
        'reservoir_types': ['大型水库'],  # 仅大型水库
        'exceed_limit': '所有',
        'alert_levels': ['1', '2', '3', '4', '5']
    }

# ============================================
# 使用示例
# ============================================

def test_custom_configs():
    """测试自定义配置"""
    
    print("水情爬虫配置示例测试")
    print("=" * 60)
    
    # 测试1: 浙江省超限水库
    print("测试1: 浙江省超限水库监测")
    config1 = ZhejiangExceedConfig()
    crawler1 = AccurateWaterCrawler(config1)
    
    water_info1 = crawler1.get_water_info()
    if water_info1:
        print(f"获取到 {len(water_info1)} 个超限水库")
        crawler1.save_to_csv(water_info1, "zhejiang_exceed_config.csv")
    
    print("\n" + "-" * 40)
    
    # 测试2: 宁波市河道潮汐
    print("测试2: 宁波市河道潮汐监测")
    config2 = NingboRiverTideConfig()
    crawler2 = AccurateWaterCrawler(config2)
    
    water_info2 = crawler2.get_water_info()
    if water_info2:
        print(f"获取到 {len(water_info2)} 个河道潮汐站点")
        crawler2.save_to_csv(water_info2, "ningbo_river_tide_config.csv")
    
    print("\n" + "-" * 40)
    
    # 测试3: 大型水库
    print("测试3: 大型水库监测")
    config3 = LargeReservoirConfig()
    crawler3 = AccurateWaterCrawler(config3)
    
    water_info3 = crawler3.get_water_info()
    if water_info3:
        print(f"获取到 {len(water_info3)} 个大型水库")
        crawler3.save_to_csv(water_info3, "large_reservoir_config.csv")

def modify_config_example():
    """动态修改配置示例"""
    
    print("\n" + "=" * 60)
    print("动态修改配置示例")
    print("=" * 60)
    
    # 创建基础配置
    config = WaterCrawlerConfig()
    
    print("原始配置:")
    print(f"  城市: {config.DEFAULT_QUERY['city']}")
    print(f"  超限筛选: {config.DEFAULT_QUERY['exceed_limit']}")
    
    # 动态修改配置
    config.DEFAULT_QUERY['city'] = '台州市'
    config.DEFAULT_QUERY['exceed_limit'] = '超限'
    config.DEFAULT_QUERY['station_categories'] = ['水库', '河道']
    
    print("\n修改后配置:")
    print(f"  城市: {config.DEFAULT_QUERY['city']}")
    print(f"  超限筛选: {config.DEFAULT_QUERY['exceed_limit']}")
    print(f"  站点类别: {config.DEFAULT_QUERY['station_categories']}")
    
    # 使用修改后的配置
    crawler = AccurateWaterCrawler(config)
    water_info = crawler.get_water_info()
    
    if water_info:
        print(f"\n获取到 {len(water_info)} 个站点")
        crawler.save_to_csv(water_info, "modified_config_water.csv")

def main():
    """主函数"""
    test_custom_configs()
    modify_config_example()
    
    print("\n" + "=" * 60)
    print("配置示例测试完成！")
    print("优势:")
    print("1. 配置集中管理，易于维护")
    print("2. 支持多种预设配置")
    print("3. 可以动态修改配置")
    print("4. 代码复用性强")

if __name__ == "__main__":
    main()
