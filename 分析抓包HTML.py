#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import json

def analyze_captured_html():
    """分析抓包文件中的HTML内容"""
    
    print("🔍 分析抓包文件中的HTML内容")
    print("="*60)
    
    try:
        # 读取抓包文件
        with open('5_Full.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"✅ 读取抓包文件，长度: {len(content)}")
        
        # 提取HTML部分（从<!DOCTYPE html>开始）
        html_start = content.find('<!DOCTYPE html>')
        if html_start == -1:
            html_start = content.find('<html')
        
        if html_start == -1:
            print("❌ 未找到HTML内容")
            return False
        
        html_content = content[html_start:]
        print(f"📄 提取HTML内容，长度: {len(html_content)}")
        
        # 保存HTML到单独文件
        with open('extracted_water_page.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        print("💾 HTML内容已保存到 'extracted_water_page.html'")
        
        # 方案A: 查找__NUXT__数据
        print(f"\n🔍 方案A: 查找__NUXT__数据")
        nuxt_result = find_nuxt_data_in_html(html_content)
        
        if nuxt_result:
            print("✅ 方案A成功!")
            return nuxt_result
        
        # 如果方案A失败，继续其他分析
        print(f"\n🔍 继续其他分析...")
        analyze_html_content(html_content)
        
        return False
        
    except Exception as e:
        print(f"❌ 分析异常: {e}")
        return False

def find_nuxt_data_in_html(html_content):
    """在HTML中查找__NUXT__数据"""
    
    print("   🔍 搜索__NUXT__模式...")
    
    # 检查是否包含__NUXT__
    if '__NUXT__' not in html_content:
        print("   ❌ HTML中不包含__NUXT__")
        return None
    
    print("   ✅ HTML中包含__NUXT__")
    
    # 多种__NUXT__数据模式
    nuxt_patterns = [
        r'window\.__NUXT__\s*=\s*({.*?});',
        r'__NUXT__\s*=\s*({.*?});',
        r'window\["__NUXT__"\]\s*=\s*({.*?});',
        r'window\.__NUXT__\s*=\s*(\{[\s\S]*?\});'  # 多行匹配
    ]
    
    for i, pattern in enumerate(nuxt_patterns, 1):
        print(f"   📊 尝试模式 {i}: {pattern[:50]}...")
        
        try:
            matches = re.findall(pattern, html_content, re.DOTALL)
            
            if matches:
                print(f"   🎯 模式 {i} 找到 {len(matches)} 个匹配")
                
                for j, match in enumerate(matches):
                    print(f"   📝 解析匹配 {j+1} (长度: {len(match)})...")
                    
                    try:
                        # 尝试解析JSON
                        data = json.loads(match)
                        print(f"   ✅ 成功解析JSON数据")
                        print(f"   📊 数据类型: {type(data)}")
                        
                        if isinstance(data, dict):
                            print(f"   🔑 顶级字段: {list(data.keys())}")
                            
                            # 查找水情数据
                            water_data = find_water_data_recursive(data)
                            if water_data:
                                print("   🎯 在__NUXT__中发现水情数据!")
                                return water_data
                            else:
                                print("   ⚠️ __NUXT__中未找到水情数据")
                                # 显示数据结构以便调试
                                print_data_structure(data, "   ")
                                
                    except json.JSONDecodeError as e:
                        print(f"   ❌ JSON解析失败: {str(e)[:100]}...")
                        # 显示匹配内容的前后部分
                        print(f"   📝 匹配内容开头: {match[:200]}...")
                        print(f"   📝 匹配内容结尾: ...{match[-200:]}")
                        continue
            else:
                print(f"   ⚠️ 模式 {i} 无匹配")
                
        except Exception as e:
            print(f"   ❌ 模式 {i} 异常: {e}")
            continue
    
    print("   ❌ 所有__NUXT__模式都失败")
    return None

def find_water_data_recursive(data, path="", depth=0):
    """递归查找水情数据"""
    
    if depth > 8:  # 限制递归深度
        return None
    
    if isinstance(data, dict):
        # 检查水情相关字段
        water_keys = ['cx', 'cjj', 'cbz', 'qt', 'water', 'station', 'reservoir', 'stations', 'data', 'list']
        
        for key in water_keys:
            if key in data:
                value = data[key]
                print(f"   🎯 发现字段 '{path}.{key}': {type(value)}")
                
                if isinstance(value, list) and value:
                    print(f"   📊 数组长度: {len(value)}")
                    
                    # 检查第一个元素
                    if len(value) > 0 and isinstance(value[0], dict):
                        first_item = value[0]
                        print(f"   🔑 第一项字段: {list(first_item.keys())}")
                        
                        # 检查是否包含水库相关信息
                        item_str = str(first_item).lower()
                        water_indicators = ['name', 'station', '水库', 'reservoir', 'code', 'lng', 'lat']
                        
                        found_indicators = [ind for ind in water_indicators if ind in item_str]
                        if found_indicators:
                            print(f"   ✅ 发现水情指标: {found_indicators}")
                            return {key: value}
                        else:
                            print(f"   ⚠️ 未发现明确的水情指标")
                
                elif isinstance(value, dict):
                    # 递归检查字典
                    result = find_water_data_recursive(value, f"{path}.{key}", depth + 1)
                    if result:
                        return result
        
        # 递归搜索其他字段
        for key, value in data.items():
            if isinstance(value, (dict, list)) and key not in water_keys:
                result = find_water_data_recursive(value, f"{path}.{key}", depth + 1)
                if result:
                    return result
                    
    elif isinstance(data, list):
        for i, item in enumerate(data):
            if isinstance(item, (dict, list)):
                result = find_water_data_recursive(item, f"{path}[{i}]", depth + 1)
                if result:
                    return result
    
    return None

def print_data_structure(data, indent="", max_depth=3, current_depth=0):
    """打印数据结构"""
    
    if current_depth >= max_depth:
        print(f"{indent}... (深度限制)")
        return
    
    if isinstance(data, dict):
        print(f"{indent}dict ({len(data)} keys):")
        for key, value in list(data.items())[:5]:  # 只显示前5个
            print(f"{indent}  {key}: {type(value).__name__}", end="")
            if isinstance(value, (list, dict)):
                if isinstance(value, list):
                    print(f" (长度: {len(value)})")
                else:
                    print(f" ({len(value)} keys)")
                print_data_structure(value, indent + "    ", max_depth, current_depth + 1)
            else:
                print(f" = {str(value)[:50]}...")
        
        if len(data) > 5:
            print(f"{indent}  ... 还有 {len(data) - 5} 个字段")
            
    elif isinstance(data, list):
        print(f"{indent}list (长度: {len(data)}):")
        if data:
            print(f"{indent}  [0]: {type(data[0]).__name__}")
            if len(data) > 0:
                print_data_structure(data[0], indent + "    ", max_depth, current_depth + 1)

def analyze_html_content(html_content):
    """分析HTML内容的其他方面"""
    
    print("   🔍 分析HTML内容...")
    
    # 统计关键词
    keywords = {
        '水库': html_content.count('水库'),
        '邵川': html_content.count('邵川'),
        '大贤溪': html_content.count('大贤溪'),
        'station': html_content.count('station'),
        'water': html_content.count('water'),
        'data': html_content.count('data'),
        'axios': html_content.count('axios'),
        'fetch': html_content.count('fetch'),
        'api': html_content.count('api')
    }
    
    print("   📊 关键词统计:")
    for keyword, count in keywords.items():
        if count > 0:
            print(f"      {keyword}: {count}次")
    
    # 查找JavaScript变量
    js_patterns = [
        (r'var\s+(\w*[Dd]ata\w*)\s*=', 'data变量'),
        (r'var\s+(\w*[Ss]tation\w*)\s*=', 'station变量'),
        (r'var\s+(\w*[Ww]ater\w*)\s*=', 'water变量')
    ]
    
    for pattern, desc in js_patterns:
        matches = re.findall(pattern, html_content)
        if matches:
            print(f"   🎯 发现{desc}: {matches}")

if __name__ == "__main__":
    print("🎯 方案A: 分析抓包文件中的HTML内容")
    print("目标: 从HTML中提取__NUXT__数据")
    
    result = analyze_captured_html()
    
    if result:
        print(f"\n{'='*60}")
        print("✅ 方案A成功!")
        print(f"找到水情数据: {list(result.keys())}")
        
        # 显示找到的数据
        for key, value in result.items():
            if isinstance(value, list):
                print(f"{key}: {len(value)}个项目")
                if value and isinstance(value[0], dict):
                    print(f"第一项字段: {list(value[0].keys())}")
        print(f"{'='*60}")
    else:
        print(f"\n{'='*60}")
        print("❌ 方案A失败")
        print("准备尝试方案B...")
        print(f"{'='*60}")
