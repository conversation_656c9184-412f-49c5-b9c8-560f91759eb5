import requests
import json
import re

def test_simple():
    print("测试方案A: 解析NUXT数据")
    
    url = "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater"
    params = {
        'areaFlag': '1',
        'sss': '温州市',
        'ssx': '永嘉县',
        'zl': 'RR,',
        'sfcj': '1'
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    try:
        print("正在请求...")
        response = requests.get(url, params=params, headers=headers, timeout=30, verify=False)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            html = response.text
            print(f"HTML长度: {len(html)}")
            
            # 保存HTML
            with open('test.html', 'w', encoding='utf-8') as f:
                f.write(html)
            print("HTML已保存")
            
            # 查找__NUXT__
            if '__NUXT__' in html:
                print("发现__NUXT__")
                pattern = r'window\.__NUXT__\s*=\s*({.*?});'
                matches = re.findall(pattern, html, re.DOTALL)
                print(f"找到{len(matches)}个匹配")
                
                if matches:
                    try:
                        data = json.loads(matches[0])
                        print(f"解析成功: {type(data)}")
                        if isinstance(data, dict):
                            print(f"字段: {list(data.keys())}")
                    except Exception as e:
                        print(f"解析失败: {e}")
            else:
                print("未发现__NUXT__")
                
            # 查找关键词
            keywords = ['水库', '邵川', 'station']
            for kw in keywords:
                count = html.count(kw)
                print(f"{kw}: {count}次")
                
        else:
            print(f"请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"异常: {e}")

if __name__ == "__main__":
    test_simple()
