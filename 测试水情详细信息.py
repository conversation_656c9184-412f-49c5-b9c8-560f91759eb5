#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from 综合预警监测系统 import ComprehensiveMonitoringSystem

def test_water_details():
    """测试水情详细信息显示"""
    system = ComprehensiveMonitoringSystem()
    
    # 测试几个区县的水情数据
    test_counties = ['温岭市', '台州市', '椒江区', '黄岩区']
    
    for county in test_counties:
        print(f"\n{'='*50}")
        print(f"测试 {county} 水情详细信息")
        print('='*50)
        
        water_data = system.get_water_data(county)
        water_summary = system.format_water_summary(water_data, county)
        print(f"🌊 {water_summary}")
        
        # 如果有站点数据，显示原始JSON数据
        if water_data and len(water_data) == 2:
            stations, type_counts = water_data
            if stations:
                print(f"\n📋 原始数据字段:")
                for i, station in enumerate(stations[:2]):  # 显示前2个站点的所有字段
                    print(f"  站点 {i+1} 的所有字段:")
                    for key, value in station.items():
                        print(f"    {key}: {value}")
                    print()  # 站点之间空行

if __name__ == "__main__":
    test_water_details()
