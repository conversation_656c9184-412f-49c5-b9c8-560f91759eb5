# HTML水情数据解析方案

## 🎯 问题分析

通过F12抓包分析，我们发现了关键问题：

### 发现的真实API
- **URL**: `https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater`
- **返回类型**: HTML页面 (不是JSON)
- **技术栈**: Nuxt.js + Element UI
- **页面长度**: ~235KB

### 技术特点
1. **单页应用(SPA)**: 基于Nuxt.js框架
2. **异步数据加载**: 数据通过JavaScript动态获取
3. **服务端渲染(SSR)**: 可能包含初始数据

## 🔍 解析策略

### 策略1: 查找Nuxt.js的__NUXT__数据
```javascript
// 在HTML中查找这种模式
window.__NUXT__ = {
  // 包含页面初始数据
  data: [...],
  state: {...}
};
```

### 策略2: 发现真实的API端点
基于Nuxt.js应用的常见模式，真实的API可能在：
- `/nuxtsyq/api/water/realtime`
- `/nuxtsyq/api/water/list`
- `/nuxtsyq/api/data/water`
- `/api/water/realtime`

### 策略3: 分析JavaScript文件
从抓包文件发现的JS文件：
- `/nuxtsyq/_nuxt/e2e57b2.js`
- `/nuxtsyq/_nuxt/5420e93.js`
- `/nuxtsyq/_nuxt/bff86e8.js`
- 等等...

## 💡 实现方案

### 方案A: 直接解析HTML中的数据

```python
def parse_nuxt_html(html_content):
    """解析Nuxt.js HTML中的数据"""
    import re
    import json
    
    # 1. 查找__NUXT__数据
    nuxt_pattern = r'window\.__NUXT__\s*=\s*({.*?});'
    matches = re.findall(nuxt_pattern, html_content, re.DOTALL)
    
    if matches:
        try:
            nuxt_data = json.loads(matches[0])
            # 递归查找水情数据
            return find_water_data_recursive(nuxt_data)
        except:
            pass
    
    # 2. 查找其他数据模式
    patterns = [
        r'waterData\s*=\s*({.*?});',
        r'stationData\s*=\s*({.*?});',
        r'realtimeData\s*=\s*({.*?});'
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, html_content, re.IGNORECASE)
        if matches:
            try:
                return json.loads(matches[0])
            except:
                continue
    
    return None
```

### 方案B: 发现并调用真实API

```python
def discover_real_api():
    """发现真实的API端点"""
    base_url = "https://sqfb.slt.zj.gov.cn:30050"
    
    # 常见的API路径
    api_paths = [
        "/nuxtsyq/api/water/realtime",
        "/nuxtsyq/api/water/list", 
        "/nuxtsyq/api/data/water",
        "/api/water/realtime"
    ]
    
    for path in api_paths:
        url = base_url + path
        try:
            response = requests.get(url, params=water_params)
            if response.status_code == 200:
                data = response.json()
                if contains_water_data(data):
                    return url, data
        except:
            continue
    
    return None, None
```

### 方案C: 分析JavaScript文件

```python
def analyze_js_files():
    """分析JavaScript文件中的API端点"""
    js_files = [
        "/nuxtsyq/_nuxt/e2e57b2.js",
        "/nuxtsyq/_nuxt/5420e93.js",
        # ... 其他文件
    ]
    
    found_apis = []
    
    for js_file in js_files:
        response = requests.get(base_url + js_file)
        if response.status_code == 200:
            js_content = response.text
            
            # 查找API端点
            api_patterns = [
                r'axios\.get\(["\']([^"\']+)["\']',
                r'fetch\(["\']([^"\']+)["\']',
                r'["\']([^"\']*api[^"\']*water[^"\']*)["\']'
            ]
            
            for pattern in api_patterns:
                matches = re.findall(pattern, js_content)
                found_apis.extend(matches)
    
    return found_apis
```

## 🛠️ 具体实现步骤

### 步骤1: 获取HTML内容
```python
url = "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater"
params = {
    'areaFlag': '1',
    'sss': '温州市',
    'ssx': '永嘉县',
    'zl': 'RR,',
    'sfcj': '1'
}

response = requests.get(url, params=params)
html_content = response.text
```

### 步骤2: 多策略解析
```python
def parse_water_data(html_content):
    # 策略1: 解析__NUXT__数据
    nuxt_data = parse_nuxt_data(html_content)
    if nuxt_data:
        return nuxt_data
    
    # 策略2: 发现API端点
    api_data = discover_api_endpoints(html_content)
    if api_data:
        return api_data
    
    # 策略3: 解析JavaScript数据
    js_data = parse_javascript_data(html_content)
    if js_data:
        return js_data
    
    return None
```

### 步骤3: 数据标准化
```python
def standardize_water_data(raw_data):
    """将不同来源的数据标准化"""
    stations = []
    
    if isinstance(raw_data, dict):
        # 处理不同的数据结构
        for key in ['cx', 'stations', 'data', 'list']:
            if key in raw_data:
                stations.extend(raw_data[key])
    
    # 标准化站点信息
    standardized = []
    for station in stations:
        standardized.append({
            'name': station.get('name', ''),
            'code': station.get('code', ''),
            'type': station.get('type', '水库'),
            'longitude': station.get('lng', 0),
            'latitude': station.get('lat', 0),
            'water_level': station.get('level', 0),
            'time': station.get('time', '')
        })
    
    return standardized
```

## 📊 预期效果

### 成功解析后的数据格式
```json
{
  "stations": [
    {
      "name": "邵川水库",
      "code": "70509614",
      "type": "水库",
      "longitude": 120.4492,
      "latitude": 28.3272,
      "water_level": 85.6,
      "time": "2025-08-04 17:00:00",
      "status": "超限"
    }
  ],
  "total": 8,
  "county": "永嘉县"
}
```

## 🔧 调试建议

### 1. 保存HTML内容
```python
with open('water_page.html', 'w', encoding='utf-8') as f:
    f.write(html_content)
```

### 2. 逐步调试
```python
# 检查HTML内容
print(f"HTML长度: {len(html_content)}")
print(f"包含'水库': {'水库' in html_content}")
print(f"包含'__NUXT__': {'__NUXT__' in html_content}")

# 查找关键词
keywords = ['邵川', '大贤溪', 'station', 'water']
for keyword in keywords:
    count = html_content.count(keyword)
    print(f"'{keyword}': {count}次")
```

### 3. 网络调试
```python
# 检查请求头
print("请求头:", response.request.headers)
print("响应头:", response.headers)
print("状态码:", response.status_code)
```

## ✅ 总结

通过F12抓包分析，我们发现了水情数据的真实来源是一个Nuxt.js应用。虽然API返回HTML而不是JSON，但我们可以通过以下方式获取数据：

1. **解析HTML中的__NUXT__数据** (最有希望)
2. **发现并调用真实的API端点**
3. **分析JavaScript文件中的数据**

关键是要理解这是一个现代的单页应用，数据获取方式与传统的API不同。通过多策略的解析方法，我们应该能够成功提取出水情数据。

## 🚀 下一步行动

1. 实现HTML内容的保存和分析
2. 重点查找__NUXT__数据结构
3. 测试发现的API端点
4. 完善数据解析和标准化逻辑
5. 集成到综合预警监测系统中
