#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import re
import urllib3
from urllib.parse import urljoin
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def analyze_nuxt_app():
    """分析Nuxt.js应用的数据获取方式"""
    
    # 基础URL
    base_url = "https://sqfb.slt.zj.gov.cn:30050"
    
    print("🔍 分析Nuxt.js应用数据获取方式")
    print("="*60)
    
    # 1. 尝试常见的Nuxt.js API路径
    nuxt_api_paths = [
        "/nuxtsyq/api/water/realtime",
        "/nuxtsyq/api/water/list", 
        "/nuxtsyq/api/data/water",
        "/nuxtsyq/_nuxt/api/water",
        "/api/water/realtime",
        "/api/water/list",
        "/api/data/water"
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Referer': 'https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater'
    }
    
    params = {
        'areaFlag': '1',
        'sss': '温州市',
        'ssx': '永嘉县',
        'zl': 'RR,',
        'sfcj': '1'
    }
    
    print("🧪 测试常见的Nuxt.js API路径")
    print("-" * 40)
    
    for api_path in nuxt_api_paths:
        url = base_url + api_path
        print(f"\n🔬 测试: {api_path}")
        
        try:
            response = requests.get(url, params=params, headers=headers, timeout=10, verify=False)
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                content_type = response.headers.get('Content-Type', '')
                print(f"  内容类型: {content_type}")
                
                if 'application/json' in content_type:
                    try:
                        data = response.json()
                        print(f"  ✅ JSON数据: {type(data)}")
                        if isinstance(data, dict) and data:
                            print(f"  字段: {list(data.keys())}")
                            # 检查是否包含水情数据
                            if any(key in str(data).lower() for key in ['water', 'station', '水库', '邵川']):
                                print(f"  🎯 可能包含水情数据!")
                                return url, data
                    except:
                        print(f"  ❌ JSON解析失败")
                else:
                    print(f"  📄 非JSON内容")
            else:
                print(f"  ❌ 失败")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
    
    # 2. 尝试分析JavaScript文件中的API端点
    print(f"\n{'='*60}")
    print("🔍 分析JavaScript文件中的API端点")
    print("="*60)
    
    # 从抓包文件中提取的JavaScript文件路径
    js_files = [
        "/nuxtsyq/_nuxt/e2e57b2.js",
        "/nuxtsyq/_nuxt/5420e93.js", 
        "/nuxtsyq/_nuxt/bff86e8.js",
        "/nuxtsyq/_nuxt/41a260e.js",
        "/nuxtsyq/_nuxt/7a9c23c.js",
        "/nuxtsyq/_nuxt/298055b.js"
    ]
    
    found_apis = []
    
    for js_file in js_files:
        url = base_url + js_file
        print(f"\n📄 分析: {js_file}")
        
        try:
            response = requests.get(url, headers=headers, timeout=10, verify=False)
            if response.status_code == 200:
                js_content = response.text
                print(f"  ✅ 获取成功，长度: {len(js_content)}")
                
                # 查找API端点
                api_patterns = [
                    r'["\']([^"\']*api[^"\']*water[^"\']*)["\']',
                    r'["\']([^"\']*water[^"\']*api[^"\']*)["\']',
                    r'["\']([^"\']*realtime[^"\']*)["\']',
                    r'["\']([^"\']*rest[^"\']*water[^"\']*)["\']',
                    r'axios\.get\(["\']([^"\']+)["\']',
                    r'fetch\(["\']([^"\']+)["\']',
                    r'\$http\.get\(["\']([^"\']+)["\']'
                ]
                
                for pattern in api_patterns:
                    matches = re.findall(pattern, js_content, re.IGNORECASE)
                    for match in matches:
                        if len(match) > 5 and ('/' in match or 'api' in match.lower()):
                            found_apis.append(match)
                
                # 查找可能的数据结构
                data_patterns = [
                    r'waterData\s*[:=]\s*({[^}]+})',
                    r'stationData\s*[:=]\s*({[^}]+})',
                    r'realtimeData\s*[:=]\s*({[^}]+})'
                ]
                
                for pattern in data_patterns:
                    matches = re.findall(pattern, js_content, re.IGNORECASE)
                    if matches:
                        print(f"  🎯 发现数据结构: {matches[0][:100]}...")
                        
            else:
                print(f"  ❌ 获取失败: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
    
    if found_apis:
        print(f"\n🎯 在JavaScript中发现的API端点:")
        unique_apis = list(set(found_apis))
        for i, api in enumerate(unique_apis[:10]):
            print(f"  {i+1}. {api}")
        
        # 测试发现的API
        print(f"\n🧪 测试发现的API端点")
        print("-" * 40)
        
        for api in unique_apis[:5]:  # 只测试前5个
            if api.startswith('/'):
                test_url = base_url + api
            elif api.startswith('http'):
                test_url = api
            else:
                continue
                
            print(f"\n🔬 测试: {api}")
            try:
                response = requests.get(test_url, params=params, headers=headers, timeout=10, verify=False)
                print(f"  状态码: {response.status_code}")
                
                if response.status_code == 200:
                    content_type = response.headers.get('Content-Type', '')
                    if 'application/json' in content_type:
                        try:
                            data = response.json()
                            print(f"  ✅ JSON数据: {type(data)}")
                            if isinstance(data, dict):
                                print(f"  字段: {list(data.keys())}")
                                return test_url, data
                        except:
                            print(f"  ❌ JSON解析失败")
                            
            except Exception as e:
                print(f"  ❌ 异常: {e}")
    
    # 3. 尝试SSR数据获取方式
    print(f"\n{'='*60}")
    print("🔍 尝试SSR数据获取方式")
    print("="*60)
    
    # Nuxt.js的SSR数据通常在__NUXT__变量中
    ssr_url = f"{base_url}/nuxtsyq/new/realtimeWater"
    
    try:
        response = requests.get(ssr_url, params=params, headers=headers, timeout=15, verify=False)
        if response.status_code == 200:
            html_content = response.text
            
            # 查找__NUXT__数据
            nuxt_pattern = r'window\.__NUXT__\s*=\s*({.*?});'
            matches = re.findall(nuxt_pattern, html_content, re.DOTALL)
            
            if matches:
                print("🎯 发现__NUXT__数据")
                try:
                    nuxt_data = json.loads(matches[0])
                    print(f"  数据类型: {type(nuxt_data)}")
                    if isinstance(nuxt_data, dict):
                        print(f"  顶级字段: {list(nuxt_data.keys())}")
                        
                        # 递归查找水情数据
                        water_data = find_water_data_recursive(nuxt_data)
                        if water_data:
                            print("  🎯 在__NUXT__中发现水情数据!")
                            return ssr_url, water_data
                            
                except Exception as e:
                    print(f"  ❌ 解析__NUXT__数据失败: {e}")
            else:
                print("  ❌ 未找到__NUXT__数据")
                
    except Exception as e:
        print(f"  ❌ SSR请求异常: {e}")
    
    return None, None

def find_water_data_recursive(data, depth=0):
    """递归查找水情数据"""
    if depth > 5:  # 限制递归深度
        return None
        
    if isinstance(data, dict):
        # 检查是否包含水情相关字段
        water_keys = ['cx', 'water', 'station', 'reservoir', 'dam']
        for key in water_keys:
            if key in data:
                return data
        
        # 检查值中是否包含水情关键词
        for key, value in data.items():
            if isinstance(value, (dict, list)):
                result = find_water_data_recursive(value, depth + 1)
                if result:
                    return result
            elif isinstance(value, str) and any(keyword in value for keyword in ['邵川', '水库', 'reservoir']):
                return data
                
    elif isinstance(data, list):
        for item in data:
            result = find_water_data_recursive(item, depth + 1)
            if result:
                return result
    
    return None

if __name__ == "__main__":
    print("🎯 Nuxt.js应用数据分析工具")
    print("目标: 找到水情数据的真实API端点")
    
    api_url, data = analyze_nuxt_app()
    
    if api_url and data:
        print(f"\n{'='*60}")
        print("✅ 成功发现水情数据API!")
        print(f"API地址: {api_url}")
        print(f"数据结构: {type(data)}")
        if isinstance(data, dict):
            print(f"字段: {list(data.keys())}")
        print(f"{'='*60}")
    else:
        print(f"\n{'='*60}")
        print("❌ 未能发现水情数据API")
        print("💡 建议:")
        print("   1. 检查网络连接")
        print("   2. 确认API参数正确性")
        print("   3. 可能需要特定的认证或会话")
        print(f"{'='*60}")
