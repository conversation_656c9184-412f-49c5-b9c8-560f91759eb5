#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import re
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_nuxt_data_parsing():
    """方案A: 解析HTML中的__NUXT__数据"""
    
    print("🔍 方案A: 解析HTML中的__NUXT__数据")
    print("="*60)
    
    # 获取HTML内容
    url = "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater"
    params = {
        'areaFlag': '1',
        'sss': '温州市',
        'ssx': '永嘉县',
        'zl': 'RR,',
        'sklx': '4,5,',
        'ly': '',
        'sfcj': '1',
        'bxdj': '1,2,3,4,5,',
        'zm': '',
        'cjly': '',
        'bx': '0'
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Referer': 'https://sqfb.slt.zj.gov.cn/',
        'Accept-Language': 'zh-CN,zh;q=0.9'
    }
    
    print("📡 正在获取HTML页面...")
    try:
        response = requests.get(url, params=params, headers=headers, timeout=30, verify=False)
        
        if response.status_code != 200:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
            
        html_content = response.text
        print(f"✅ 成功获取HTML，长度: {len(html_content)}")
        
        # 保存HTML文件以便分析
        with open('永嘉县水情页面.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        print("💾 HTML已保存到 '永嘉县水情页面.html'")
        
        # 步骤1: 查找__NUXT__数据
        print(f"\n🔍 步骤1: 查找__NUXT__数据")
        nuxt_data = find_nuxt_data(html_content)
        
        if nuxt_data:
            print("✅ 成功找到__NUXT__数据")
            water_data = extract_water_from_nuxt(nuxt_data)
            if water_data:
                print("🎯 成功从__NUXT__中提取水情数据!")
                return water_data
        
        # 步骤2: 查找其他JavaScript数据
        print(f"\n🔍 步骤2: 查找其他JavaScript数据")
        js_data = find_javascript_data(html_content)
        
        if js_data:
            print("✅ 成功找到JavaScript数据")
            return js_data
        
        # 步骤3: 分析HTML结构
        print(f"\n🔍 步骤3: 分析HTML结构")
        analyze_html_structure(html_content)
        
        return None
        
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def find_nuxt_data(html_content):
    """查找__NUXT__数据"""
    print("   🔍 搜索__NUXT__模式...")
    
    # 多种__NUXT__数据模式
    nuxt_patterns = [
        r'window\.__NUXT__\s*=\s*({.*?});',
        r'__NUXT__\s*=\s*({.*?});',
        r'window\["__NUXT__"\]\s*=\s*({.*?});',
        r'window\.__NUXT__\s*=\s*(\{[\s\S]*?\});'  # 多行匹配
    ]
    
    for i, pattern in enumerate(nuxt_patterns, 1):
        print(f"   📊 尝试模式 {i}...")
        matches = re.findall(pattern, html_content, re.DOTALL)
        
        if matches:
            print(f"   🎯 模式 {i} 找到 {len(matches)} 个匹配")
            
            for j, match in enumerate(matches):
                try:
                    print(f"   📝 解析匹配 {j+1} (长度: {len(match)})...")
                    data = json.loads(match)
                    print(f"   ✅ 成功解析JSON数据")
                    print(f"   📊 数据类型: {type(data)}")
                    
                    if isinstance(data, dict):
                        print(f"   🔑 顶级字段: {list(data.keys())}")
                        return data
                        
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON解析失败: {str(e)[:100]}...")
                    continue
    
    print("   ⚠️ 未找到有效的__NUXT__数据")
    return None

def extract_water_from_nuxt(nuxt_data):
    """从__NUXT__数据中提取水情信息"""
    print("   🔍 在__NUXT__数据中查找水情信息...")
    
    def search_recursive(data, path="", depth=0):
        if depth > 10:  # 限制递归深度
            return None
            
        if isinstance(data, dict):
            # 检查水情相关字段
            water_keys = ['cx', 'cjj', 'cbz', 'qt', 'water', 'station', 'reservoir', 'stations', 'data']
            
            for key in water_keys:
                if key in data:
                    value = data[key]
                    print(f"   🎯 发现字段 '{path}.{key}': {type(value)}")
                    
                    if isinstance(value, list) and value:
                        print(f"   📊 数组长度: {len(value)}")
                        if isinstance(value[0], dict):
                            print(f"   🔑 第一项字段: {list(value[0].keys())}")
                            
                            # 检查是否包含水库信息
                            first_item = value[0]
                            if any(field in str(first_item).lower() for field in ['name', 'station', '水库']):
                                print(f"   ✅ 疑似水情数据!")
                                return {key: value}
            
            # 递归搜索
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    result = search_recursive(value, f"{path}.{key}", depth + 1)
                    if result:
                        return result
                        
        elif isinstance(data, list):
            for i, item in enumerate(data):
                result = search_recursive(item, f"{path}[{i}]", depth + 1)
                if result:
                    return result
        
        return None
    
    result = search_recursive(nuxt_data)
    
    if result:
        print(f"   ✅ 找到水情数据: {list(result.keys())}")
        return result
    else:
        print(f"   ⚠️ 未在__NUXT__中找到水情数据")
        return None

def find_javascript_data(html_content):
    """查找其他JavaScript数据"""
    print("   🔍 搜索其他JavaScript数据模式...")
    
    js_patterns = [
        (r'var\s+waterData\s*=\s*({.*?});', 'waterData变量'),
        (r'var\s+stationData\s*=\s*({.*?});', 'stationData变量'),
        (r'var\s+realtimeData\s*=\s*({.*?});', 'realtimeData变量'),
        (r'data\s*:\s*({.*?})', 'data对象'),
        (r'stations\s*:\s*(\[.*?\])', 'stations数组'),
        (r'waterStations\s*=\s*(\[.*?\]);', 'waterStations数组')
    ]
    
    for pattern, desc in js_patterns:
        print(f"   📊 搜索 {desc}...")
        matches = re.findall(pattern, html_content, re.DOTALL | re.IGNORECASE)
        
        if matches:
            print(f"   🎯 找到 {len(matches)} 个 {desc}")
            
            for match in matches:
                try:
                    data = json.loads(match)
                    print(f"   ✅ 成功解析 {desc}")
                    
                    # 检查是否包含水情数据
                    if contains_water_info(data):
                        print(f"   🎯 {desc} 包含水情信息!")
                        return data
                        
                except json.JSONDecodeError:
                    continue
    
    print("   ⚠️ 未找到其他JavaScript数据")
    return None

def contains_water_info(data):
    """检查数据是否包含水情信息"""
    data_str = str(data).lower()
    water_keywords = ['水库', 'reservoir', 'station', 'water', '邵川', '大贤溪']
    
    return any(keyword in data_str for keyword in water_keywords)

def analyze_html_structure(html_content):
    """分析HTML结构"""
    print("   🔍 分析HTML结构...")
    
    # 统计关键词出现次数
    keywords = {
        '水库': html_content.count('水库'),
        '邵川': html_content.count('邵川'),
        '大贤溪': html_content.count('大贤溪'),
        'station': html_content.count('station'),
        'water': html_content.count('water'),
        '__NUXT__': html_content.count('__NUXT__'),
        'data': html_content.count('data'),
        'axios': html_content.count('axios'),
        'fetch': html_content.count('fetch')
    }
    
    print("   📊 关键词统计:")
    for keyword, count in keywords.items():
        if count > 0:
            print(f"      {keyword}: {count}次")
    
    # 查找可能的API调用
    api_patterns = [
        r'axios\.get\(["\']([^"\']+)["\']',
        r'fetch\(["\']([^"\']+)["\']',
        r'\$http\.get\(["\']([^"\']+)["\']',
        r'["\']([^"\']*api[^"\']*)["\']',
        r'["\']([^"\']*rest[^"\']*)["\']'
    ]
    
    found_apis = set()
    for pattern in api_patterns:
        matches = re.findall(pattern, html_content, re.IGNORECASE)
        for match in matches:
            if len(match) > 5 and ('/' in match or 'api' in match.lower()):
                found_apis.add(match)
    
    if found_apis:
        print("   🎯 发现可能的API端点:")
        for api in sorted(found_apis)[:10]:
            print(f"      {api}")
    
    # 查找script标签
    script_matches = re.findall(r'<script[^>]*src=["\']([^"\']+)["\']', html_content)
    if script_matches:
        print(f"   📄 发现 {len(script_matches)} 个外部脚本文件")
        for script in script_matches[:5]:
            print(f"      {script}")

if __name__ == "__main__":
    print("🎯 方案A测试: 解析HTML中的__NUXT__数据")
    print("目标: 从Nuxt.js应用中提取水情数据")
    
    result = test_nuxt_data_parsing()
    
    if result:
        print(f"\n{'='*60}")
        print("✅ 方案A成功!")
        print(f"提取的数据: {result}")
        print(f"{'='*60}")
    else:
        print(f"\n{'='*60}")
        print("❌ 方案A未成功")
        print("将继续尝试方案B...")
        print(f"{'='*60}")
