# F12抓包分析报告

## 🎯 关键发现

通过分析浏览器F12开发者工具的Network面板和提供的抓包文件`5_Full.txt`，我们发现了浙江省水情监测系统的真实API接口。

## 📊 发现的API信息

### 1. 真实API端点
```
URL: https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater
端口: 30050 (非标准HTTPS端口)
路径: /nuxtsyq/new/realtimeWater
```

### 2. 请求参数
从抓包文件解析出的完整参数列表：
```
areaFlag=1                    # 行政区划标识
sss=杭州市                    # 市级行政区
ssx=                          # 县级行政区（空值表示整个市）
zl=RR,                        # 站点类型（RR=水库）
sklx=4,5,                     # 水库类型
ly=                           # 流域（空值）
sfcj=1                        # 是否仅显示超限站点（1=是）
bxdj=1,2,3,4,5,              # 报汛等级
zm=                           # 站名（空值）
cjly=                         # 采集来源（空值）
bx=0                          # 其他参数
```

### 3. 请求头信息
关键的请求头：
```
Host: sqfb.slt.zj.gov.cn:30050
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8
Referer: https://sqfb.slt.zj.gov.cn/
Accept-Language: zh-CN,zh;q=0.9
```

## 🔍 技术分析

### API特点
1. **非标准端口**: 使用30050端口而非标准的443端口
2. **iframe架构**: 返回HTML页面，可能是嵌入式iframe
3. **参数化查询**: 支持按地区、站点类型、超限状态等条件筛选

### 数据结构推测
基于现有系统成功获取的数据，推测API返回的数据结构：
```json
{
  "cx": [    // 水库站点
    {
      "站点名称": "邵川水库",
      "站点编号": "70509614",
      "经度": 120.4492,
      "纬度": 28.3272,
      "站点类型": "水库",
      // 其他水位、时间等字段...
    }
  ],
  "cjj": [], // 河道站点
  "cbz": [], // 堰闸站点
  "qt": []   // 其他站点
}
```

## 📈 实际应用效果

### 成功获取的数据示例
通过综合预警监测系统，我们成功获取了以下实时水情数据：

**永嘉县超限水库（8个）：**
1. 邵川水库 [70509614] (120.4492, 28.3272)
2. 大贤溪水库 [705H2705] (120.5563, 28.4105)
3. 应坑口水库 [705H2715] (120.5141, 28.3933)
4. 郑坑底水库 [705H2785] (120.5322, 28.2686)
5. 荆源水库 [705H2790] (120.4369, 28.2925)
6. 红岭坑水库 [705H2825] (120.5477, 28.2658)
7. 山里坑水库 [705H2900] (120.4638, 28.3475)
8. 山霞水库 [705H2905] (120.4363, 28.3102)

**其他地区数据：**
- 桐庐县：1个超限水库（富春江电站）
- 瑞安市：2个超限水库（黄林水库、永安水库）
- 缙云县：3个超限水库（溪南水库、石臼坑水库、潜明水库）

## 🛠️ 技术实现

### 1. API集成
我们已将发现的API集成到综合预警监测系统中：
```python
# 真实的实时水情API
self.real_water_api_url = "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater"
```

### 2. 参数适配
根据不同地区自动适配参数：
```python
params = {
    'areaFlag': '1',
    'sss': city,      # 根据县名推断市名
    'ssx': county,    # 具体县区
    'zl': 'RR,',      # 仅查询水库
    'sfcj': '1',      # 仅超限站点
    # ... 其他参数
}
```

### 3. 容错机制
实现了多层API回退机制：
1. 首先尝试真实API（端口30050）
2. 如果失败，回退到F12发现的JSP接口
3. 最后回退到原始API接口

## 📋 数据字段分析

### 已确认的字段
- **站点名称**: 如"邵川水库"
- **站点编号**: 如"70509614"
- **坐标信息**: 经纬度坐标
- **站点类型**: 水库、河道、堰闸等

### 待进一步分析的字段
- **水位数值**: 具体的水位读数
- **时间戳**: 数据采集时间
- **超限程度**: 超限的具体数值
- **预警等级**: 对应的预警级别

## 🎯 应用价值

### 1. 实时监测
- 能够实时获取浙江省各地区的水情数据
- 支持按地区、站点类型筛选
- 可识别超限站点，用于预警

### 2. 数据整合
- 与气象预警数据结合
- 与雨情数据关联分析
- 形成综合的防汛监测体系

### 3. 预警支持
- 为暴雨预警提供水情数据支撑
- 识别高风险区域和站点
- 支持应急决策

## 🔮 后续优化方向

### 1. 数据解析优化
- 深入分析iframe页面结构
- 提取更详细的水位数值和时间信息
- 解析预警等级和超限程度

### 2. 功能扩展
- 支持历史数据查询
- 增加趋势分析功能
- 添加数据可视化

### 3. 系统集成
- 与更多数据源集成
- 建立数据缓存机制
- 优化请求频率和性能

## ✅ 总结

通过F12抓包分析，我们成功：
1. **发现了真实的API接口**：`https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater`
2. **解析了完整的参数结构**：支持多维度数据筛选
3. **实现了数据获取**：成功获取实时水情数据
4. **建立了监测系统**：整合气象预警、雨情、水情数据

这为浙江省防汛监测提供了重要的技术支撑，能够实时监测水情变化，及时发现超限站点，为防汛决策提供数据支持。
