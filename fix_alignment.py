#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def fix_alignment():
    """修复对齐问题"""
    with open('综合预警监测系统.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复雨情详细信息的缩进 - 从4个空格改为2个空格
    content = re.sub(r'                print\(f"    [^"]*️', '                print(f"  🌦️', content)
    
    # 修复水情标题的缩进 - 从2个空格改为无缩进
    content = re.sub(r'                print\(f"  🌊', '                print(f"🌊', content)
    
    with open('综合预警监测系统.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print('对齐修复完成')

if __name__ == "__main__":
    fix_alignment()
