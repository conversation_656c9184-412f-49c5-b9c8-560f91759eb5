#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import urllib3
from datetime import datetime
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def analyze_water_data_structure():
    """详细分析水情数据结构，寻找水位值和时间信息"""
    
    # 使用F12发现的实时API
    url = "https://sqfb.slt.zj.gov.cn/rest/newList/getNewDataListRealTime.jsp"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Referer': 'https://sqfb.slt.zj.gov.cn/winindex.html',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive'
    }
    
    # 测试永嘉县（从运行结果看有8个超限水库）
    params = {
        'areaFlag': '1',
        'sss': '温州市',
        'ssx': '永嘉县',
        'zl': 'RR,',  # 只查水库
        'sfcj': '1'   # 超限站点
    }
    
    print("🔍 详细分析永嘉县水情数据结构")
    print("="*60)
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=30, verify=False)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 成功获取数据，状态码: {response.status_code}")
            print(f"📊 数据类型: {type(data)}")
            
            if isinstance(data, dict):
                print(f"🔑 顶级字段: {list(data.keys())}")
                
                # 分析每个字段
                for key, value in data.items():
                    print(f"\n--- 分析字段: {key} ---")
                    print(f"类型: {type(value)}")
                    
                    if isinstance(value, list):
                        print(f"数组长度: {len(value)}")
                        
                        if value:  # 如果数组不为空
                            print(f"第一项类型: {type(value[0])}")
                            
                            if isinstance(value[0], dict):
                                first_item = value[0]
                                print(f"第一项字段: {list(first_item.keys())}")
                                
                                # 详细显示第一项的所有数据
                                print("\n🎯 第一项详细数据:")
                                for field, val in first_item.items():
                                    print(f"  {field}: {val} ({type(val).__name__})")
                                
                                # 分析可能的水位和时间字段
                                print("\n🔍 字段分析:")
                                water_level_candidates = []
                                time_candidates = []
                                
                                for field, val in first_item.items():
                                    field_lower = field.lower()
                                    
                                    # 查找可能的水位字段
                                    if any(keyword in field_lower for keyword in 
                                          ['water', 'level', 'sw', 'z', 'value', 'val', 'height', 'depth']):
                                        water_level_candidates.append((field, val))
                                    
                                    # 查找可能的时间字段
                                    if any(keyword in field_lower for keyword in 
                                          ['time', 'tm', 'sj', 'date', 'dt', 'timestamp']):
                                        time_candidates.append((field, val))
                                    
                                    # 查找数值型字段（可能是水位）
                                    if isinstance(val, (int, float)) and val > 0:
                                        if field not in [f[0] for f in water_level_candidates]:
                                            water_level_candidates.append((field, val))
                                
                                if water_level_candidates:
                                    print("💧 可能的水位字段:")
                                    for field, val in water_level_candidates:
                                        print(f"  - {field}: {val}")
                                
                                if time_candidates:
                                    print("⏰ 可能的时间字段:")
                                    for field, val in time_candidates:
                                        print(f"  - {field}: {val}")
                                
                                # 如果有多个项目，比较差异
                                if len(value) > 1:
                                    print(f"\n🔄 比较前两项的差异:")
                                    second_item = value[1]
                                    for field in first_item.keys():
                                        val1 = first_item.get(field)
                                        val2 = second_item.get(field)
                                        if val1 != val2:
                                            print(f"  {field}: {val1} → {val2}")
                    else:
                        print(f"值: {value}")
                
                # 尝试获取所有站点的水位数据
                print(f"\n{'='*60}")
                print("📋 所有超限水库详细信息")
                print(f"{'='*60}")
                
                all_stations = []
                for key in ['cx', 'cjj', 'cbz', 'qt']:  # 不同类型的站点
                    if key in data and data[key]:
                        stations = data[key]
                        all_stations.extend(stations)
                        print(f"\n{key}类型站点: {len(stations)}个")
                
                for i, station in enumerate(all_stations, 1):
                    print(f"\n站点 {i}:")
                    for field, value in station.items():
                        print(f"  {field}: {value}")
                    
                    if i >= 3:  # 只显示前3个站点的详细信息
                        print(f"  ... (还有{len(all_stations)-3}个站点)")
                        break
                        
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_different_parameters():
    """测试不同参数组合，寻找更多数据"""
    
    url = "https://sqfb.slt.zj.gov.cn/rest/newList/getNewDataListRealTime.jsp"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Referer': 'https://sqfb.slt.zj.gov.cn/winindex.html'
    }
    
    # 测试不同的参数组合
    test_cases = [
        {
            'name': '永嘉县-所有站点',
            'params': {
                'areaFlag': '1',
                'sss': '温州市',
                'ssx': '永嘉县',
                'zl': 'RR,ZZ,ZQ,DD,TT,',
                'sfcj': '0'  # 所有站点，不只是超限
            }
        },
        {
            'name': '永嘉县-仅超限',
            'params': {
                'areaFlag': '1',
                'sss': '温州市',
                'ssx': '永嘉县',
                'zl': 'RR,',
                'sfcj': '1'  # 仅超限
            }
        },
        {
            'name': '瑞安市-仅超限',
            'params': {
                'areaFlag': '1',
                'sss': '温州市',
                'ssx': '瑞安市',
                'zl': 'RR,',
                'sfcj': '1'
            }
        }
    ]
    
    print(f"\n{'='*60}")
    print("🧪 测试不同参数组合")
    print(f"{'='*60}")
    
    for test_case in test_cases:
        print(f"\n🔬 测试: {test_case['name']}")
        print("-" * 40)
        
        try:
            response = requests.get(url, params=test_case['params'], headers=headers, timeout=15, verify=False)
            
            if response.status_code == 200:
                data = response.json()
                
                # 统计各类型站点数量
                total_stations = 0
                for key in ['cx', 'cjj', 'cbz', 'qt']:
                    if key in data and data[key]:
                        count = len(data[key])
                        total_stations += count
                        print(f"  {key}: {count}个站点")
                
                print(f"  总计: {total_stations}个站点")
                
                # 如果有数据，显示第一个站点的字段
                for key in ['cx', 'cjj', 'cbz', 'qt']:
                    if key in data and data[key] and len(data[key]) > 0:
                        first_station = data[key][0]
                        print(f"  {key}类型站点字段: {list(first_station.keys())}")
                        break
                        
            else:
                print(f"  ❌ 失败，状态码: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")

if __name__ == "__main__":
    print("🔍 详细分析水情数据结构")
    print("基于F12发现的实时API: getNewDataListRealTime.jsp")
    
    analyze_water_data_structure()
    test_different_parameters()
    
    print(f"\n{'='*60}")
    print("✅ 分析完成")
    print("💡 关键发现:")
    print("   - 实时API可以获取超限水库数据")
    print("   - 每个水库包含详细的字段信息")
    print("   - 需要进一步分析字段含义找到水位值")
    print(f"{'='*60}")
