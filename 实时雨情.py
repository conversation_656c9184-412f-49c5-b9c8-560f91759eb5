import requests
import json
from datetime import datetime, timedelta

def get_rainfall_data(start_time=None, end_time=None, area_flag=1, min_rain=0, max_rain=None,
                     warning_levels=[1,2,3,4,5,6], data_type=0, city=None, county=None):
    """
    获取实时雨情数据
    
    参数:
    start_time: 开始时间，格式为 'YYYY-MM-DDTHH:MM:SS'，默认为当前时间前24小时
    end_time: 结束时间，格式为 'YYYY-MM-DDTHH:MM:SS'，默认为当前时间
    area_flag: 区域标志，默认为1
    min_rain: 最小降雨量，默认为0
    max_rain: 最大降雨量，默认为空
    warning_levels: 预警等级列表，默认为[1,2,3,4,5,6]
    data_type: 数据类型，默认为0
    
    返回:
    字典格式的雨情数据
    """
    
    # 如果没有指定时间，使用默认时间范围（过去24小时）
    if not end_time:
        end_time = datetime.now().strftime('%Y-%m-%dT%H:00:00')
    if not start_time:
        start_dt = datetime.now() - timedelta(hours=24)
        start_time = start_dt.strftime('%Y-%m-%dT%H:00:00')
    
    # 构建API URL
    base_url = "https://sqfb.slt.zj.gov.cn/rest/newList/getNewTotalRainList"
    
    # 构建查询参数（根据抓包信息优化）
    params = {
        'areaFlag': area_flag,
        'st': start_time,
        'et': end_time,
        'min': min_rain,
        'bool': 'false',  # 根据抓包信息修正
        'bxdj': ','.join(map(str, warning_levels)) + ',',  # 末尾加逗号
        'type': data_type,
        'ly': '',         # 流域
        'zm': '',         # 站名
        'lx': 'QX,ME,SX,DS'  # 站点类型
    }

    # 如果指定了市和县，添加区域筛选
    if city:
        params['sss'] = city
    if county:
        params['ssx'] = county

    # 如果指定了最大降雨量，添加到参数中
    if max_rain is not None:
        params['max'] = max_rain
    else:
        params['max'] = ''
    
    try:
        # 发送GET请求
        print(f"正在获取雨情数据...")
        print(f"时间范围: {start_time} 到 {end_time}")
        
        response = requests.get(base_url, params=params, timeout=30)
        response.raise_for_status()  # 检查HTTP错误
        
        # 解析JSON响应
        data = response.json()
        
        print(f"成功获取数据！")
        return data
        
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return None
    except Exception as e:
        print(f"未知错误: {e}")
        return None

def display_rainfall_summary(data):
    """
    显示雨情数据摘要
    """
    if not data:
        print("没有数据可显示")
        return
    
    print("\n=== 雨情数据摘要 ===")
    
    # 如果数据包含列表
    if isinstance(data, dict) and 'data' in data:
        rainfall_list = data['data']
        if isinstance(rainfall_list, list):
            print(f"共获取到 {len(rainfall_list)} 条雨情记录")
            
            if rainfall_list:
                print("\n前5条记录:")
                for i, record in enumerate(rainfall_list[:5]):
                    print(f"{i+1}. {record}")
        else:
            print("数据格式不符合预期")
    else:
        print("数据结构:")
        print(json.dumps(data, ensure_ascii=False, indent=2))

def save_to_file(data, filename=None):
    """
    将数据保存到文件
    """
    if not data:
        print("没有数据可保存")
        return
    
    if not filename:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"rainfall_data_{timestamp}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到: {filename}")
    except Exception as e:
        print(f"保存文件时出错: {e}")

def main():
    """
    主函数
    """
    print("=== 浙江省实时雨情数据获取工具 ===")
    
    # 获取雨情数据
    rainfall_data = get_rainfall_data()
    
    if rainfall_data:
        # 显示数据摘要
        display_rainfall_summary(rainfall_data)
        save_to_file(rainfall_data)
        print("\n程序执行完成！")
    else:
        print("获取数据失败，请检查网络连接或API接口状态。")

if __name__ == "__main__":
    main()