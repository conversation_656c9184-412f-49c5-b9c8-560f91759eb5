#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import re
import urllib3
from urllib.parse import urljoin, urlparse
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def analyze_html_water_data():
    """分析HTML页面中的水情数据"""
    
    # 真实API
    url = "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater"
    
    # 测试永嘉县参数
    params = {
        'areaFlag': '1',
        'sss': '温州市',
        'ssx': '永嘉县',
        'zl': 'RR,',
        'sklx': '4,5,',
        'ly': '',
        'sfcj': '1',
        'bxdj': '1,2,3,4,5,',
        'zm': '',
        'cjly': '',
        'bx': '0'
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Referer': 'https://sqfb.slt.zj.gov.cn/',
        'Accept-Language': 'zh-CN,zh;q=0.9'
    }
    
    print("🔍 分析HTML页面中的水情数据")
    print("="*60)
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=30, verify=False)
        
        if response.status_code == 200:
            html_content = response.text
            print(f"✅ 获取HTML页面，长度: {len(html_content)}")
            
            # 保存HTML到文件以便分析
            with open('水情页面.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            print("📄 HTML内容已保存到 '水情页面.html'")
            
            # 分析1: 查找JavaScript数据
            print(f"\n{'='*60}")
            print("🔍 分析1: 查找JavaScript中的数据")
            print("="*60)
            
            js_patterns = [
                (r'var\s+(\w+)\s*=\s*({.*?});', 'var变量'),
                (r'window\.__NUXT__\s*=\s*({.*?});', 'NUXT数据'),
                (r'data\s*:\s*({.*?})', 'data对象'),
                (r'(\w+Data)\s*=\s*({.*?});', '数据变量'),
                (r'axios\.get\(["\']([^"\']+)["\']', 'axios请求'),
                (r'fetch\(["\']([^"\']+)["\']', 'fetch请求')
            ]
            
            for pattern, desc in js_patterns:
                matches = re.findall(pattern, html_content, re.DOTALL | re.IGNORECASE)
                if matches:
                    print(f"\n🎯 发现{desc}: {len(matches)}个")
                    for i, match in enumerate(matches[:3]):  # 只显示前3个
                        if isinstance(match, tuple):
                            if len(match) == 2:
                                var_name, data = match
                                print(f"  {i+1}. {var_name}: {data[:100]}...")
                            else:
                                print(f"  {i+1}. {match[0]}")
                        else:
                            print(f"  {i+1}. {match[:100]}...")
            
            # 分析2: 查找API端点
            print(f"\n{'='*60}")
            print("🔍 分析2: 查找API端点")
            print("="*60)
            
            api_patterns = [
                r'["\']([^"\']*api[^"\']*)["\']',
                r'["\']([^"\']*rest[^"\']*)["\']',
                r'["\']([^"\']*water[^"\']*)["\']',
                r'["\']([^"\']*realtime[^"\']*)["\']',
                r'url\s*:\s*["\']([^"\']+)["\']',
                r'endpoint\s*:\s*["\']([^"\']+)["\']'
            ]
            
            found_apis = set()
            for pattern in api_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    if len(match) > 5 and ('/' in match or 'http' in match):
                        found_apis.add(match)
            
            if found_apis:
                print(f"🎯 发现可能的API端点: {len(found_apis)}个")
                for i, api in enumerate(sorted(found_apis)[:10]):  # 只显示前10个
                    print(f"  {i+1}. {api}")
            
            # 分析3: 查找表格和数据结构
            print(f"\n{'='*60}")
            print("🔍 分析3: 查找表格和数据结构")
            print("="*60)
            
            # 查找表格
            table_matches = re.findall(r'<table[^>]*>(.*?)</table>', html_content, re.DOTALL | re.IGNORECASE)
            if table_matches:
                print(f"🎯 发现表格: {len(table_matches)}个")
                for i, table in enumerate(table_matches[:2]):
                    print(f"  表格{i+1}: {table[:200]}...")
            
            # 查找列表
            list_matches = re.findall(r'<ul[^>]*>(.*?)</ul>', html_content, re.DOTALL | re.IGNORECASE)
            if list_matches:
                print(f"🎯 发现列表: {len(list_matches)}个")
            
            # 分析4: 查找特定的水情关键词
            print(f"\n{'='*60}")
            print("🔍 分析4: 查找水情关键词")
            print("="*60)
            
            water_keywords = ['水库', '水位', '超限', '邵川', '大贤溪', '应坑口', '站点', 'station', 'reservoir']
            for keyword in water_keywords:
                count = html_content.count(keyword)
                if count > 0:
                    print(f"🎯 关键词 '{keyword}': 出现{count}次")
                    
                    # 查找包含关键词的上下文
                    pattern = f'.{{0,50}}{re.escape(keyword)}.{{0,50}}'
                    contexts = re.findall(pattern, html_content, re.IGNORECASE)
                    if contexts:
                        print(f"  上下文示例: {contexts[0]}")
            
            # 分析5: 尝试解析可能的JSON数据
            print(f"\n{'='*60}")
            print("🔍 分析5: 尝试解析JSON数据")
            print("="*60)
            
            # 查找可能的JSON结构
            json_patterns = [
                r'({[^{}]*"[^"]*"[^{}]*:[^{}]*})',
                r'(\[[^[\]]*{[^{}]*}[^[\]]*\])'
            ]
            
            for pattern in json_patterns:
                matches = re.findall(pattern, html_content)
                valid_json_count = 0
                for match in matches:
                    try:
                        data = json.loads(match)
                        valid_json_count += 1
                        if valid_json_count <= 3:  # 只显示前3个
                            print(f"  有效JSON {valid_json_count}: {str(data)[:100]}...")
                    except:
                        continue
                
                if valid_json_count > 0:
                    print(f"🎯 发现有效JSON: {valid_json_count}个")
            
            # 分析6: 查找iframe或嵌入内容
            print(f"\n{'='*60}")
            print("🔍 分析6: 查找iframe和嵌入内容")
            print("="*60)
            
            iframe_matches = re.findall(r'<iframe[^>]*src=["\']([^"\']+)["\']', html_content, re.IGNORECASE)
            if iframe_matches:
                print(f"🎯 发现iframe: {len(iframe_matches)}个")
                for i, src in enumerate(iframe_matches):
                    print(f"  iframe{i+1}: {src}")
            
            # 查找script标签中的src
            script_matches = re.findall(r'<script[^>]*src=["\']([^"\']+)["\']', html_content, re.IGNORECASE)
            if script_matches:
                print(f"🎯 发现外部脚本: {len(script_matches)}个")
                for i, src in enumerate(script_matches[:5]):
                    print(f"  脚本{i+1}: {src}")
            
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 分析异常: {e}")

def test_discovered_apis():
    """测试发现的API端点"""
    
    # 基于分析可能发现的API端点
    potential_apis = [
        "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/api/water/realtime",
        "https://sqfb.slt.zj.gov.cn:30050/api/water/data",
        "https://sqfb.slt.zj.gov.cn:30050/rest/water/list",
        "https://sqfb.slt.zj.gov.cn/rest/newList/getNewDataList",
        "https://sqfb.slt.zj.gov.cn/api/water/realtime"
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Referer': 'https://sqfb.slt.zj.gov.cn:30050/'
    }
    
    params = {
        'areaFlag': '1',
        'sss': '温州市',
        'ssx': '永嘉县',
        'zl': 'RR,',
        'sfcj': '1'
    }
    
    print(f"\n{'='*60}")
    print("🧪 测试可能的API端点")
    print("="*60)
    
    for api_url in potential_apis:
        print(f"\n🔬 测试: {api_url}")
        try:
            response = requests.get(api_url, params=params, headers=headers, timeout=10, verify=False)
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                content_type = response.headers.get('Content-Type', '')
                print(f"  内容类型: {content_type}")
                
                if 'application/json' in content_type:
                    try:
                        data = response.json()
                        print(f"  ✅ JSON数据: {type(data)}")
                        if isinstance(data, dict):
                            print(f"  字段: {list(data.keys())}")
                    except:
                        print(f"  ❌ JSON解析失败")
                else:
                    print(f"  📄 非JSON内容，长度: {len(response.text)}")
            else:
                print(f"  ❌ 请求失败")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")

if __name__ == "__main__":
    print("🔍 HTML水情数据分析工具")
    print("目标: 从HTML页面中提取水情数据")
    
    analyze_html_water_data()
    test_discovered_apis()
    
    print(f"\n{'='*60}")
    print("✅ 分析完成")
    print("💡 请查看生成的 '水情页面.html' 文件进行进一步分析")
    print(f"{'='*60}")
