#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
浙江省水情信息爬虫
基于抓包分析的实时水情数据获取器
"""

import requests
import json
import csv
import time
from datetime import datetime
from urllib.parse import quote

class WaterInfoCrawler:
    def __init__(self):
        self.base_url = "https://sqfb.slt.zj.gov.cn/rest/newList/getNewDataList"
        
        # 请求头 (基于抓包)
        self.headers = {
            'Host': 'sqfb.slt.zj.gov.cn',
            'Connection': 'keep-alive',
            'sec-ch-ua-platform': '"Windows"',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://sqfb.slt.zj.gov.cn/weIndex.html',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'zh-CN,zh;q=0.9'
        }
        
        # 参数映射
        self.area_map = {
            '杭州市': '杭州市',
            '宁波市': '宁波市', 
            '温州市': '温州市',
            '嘉兴市': '嘉兴市',
            '湖州市': '湖州市',
            '绍兴市': '绍兴市',
            '金华市': '金华市',
            '衢州市': '衢州市',
            '舟山市': '舟山市',
            '台州市': '台州市',
            '丽水市': '丽水市'
        }
        
        # 站点类型映射
        self.station_types = {
            '水库': '4',
            '河道': '5', 
            '湖泊': '3',
            '闸坝': '2',
            '泵站': '1',
            '其他': '9'
        }
        
        # 报汛等级映射
        self.alert_levels = {
            '一级': '1',
            '二级': '2',
            '三级': '3', 
            '四级': '4',
            '五级': '5'
        }

    def get_water_info(self, city='宁波市', district='海曙区', station_types=None, alert_levels=None):
        """
        获取水情信息
        
        Args:
            city: 城市名称
            district: 区县名称
            station_types: 站点类型列表，如['水库', '河道']
            alert_levels: 报汛等级列表，如['1', '2', '3']
        """
        
        # 默认参数
        if station_types is None:
            station_types = ['水库', '河道', '湖泊', '闸坝', '泵站', '其他']
        if alert_levels is None:
            alert_levels = ['1', '2', '3', '4', '5']
        
        # 转换站点类型
        sklx_codes = [self.station_types.get(st, '4') for st in station_types]
        
        # 构建请求参数
        params = {
            'areaFlag': '1',  # 区域标志
            'sss': city,      # 省市
            'ssx': district,  # 区县
            'zl': 'RR,',      # 站点类别
            'sklx': ','.join(sklx_codes) + ',',  # 站点类型
            'ly': '',         # 来源
            'sfcj': '1',      # 是否采集
            'bxdj': ','.join(alert_levels) + ',',  # 报汛等级
            'zm': '',         # 站名
            'cjly': '',       # 采集来源
            'bx': '0'         # 报汛
        }
        
        try:
            print(f"正在获取 {city} {district} 的水情信息...")
            
            response = requests.get(
                self.base_url,
                params=params,
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"获取成功，状态码: {response.status_code}")
                return self.parse_water_data(data)
            else:
                print(f"请求失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"请求异常: {e}")
            return None

    def parse_water_data(self, data):
        """解析水情数据"""
        if not data:
            print("数据格式错误或无数据")
            return []

        water_stations = []

        # 处理不同类型的站点数据
        station_types = {
            'cjj': '采集井',
            'qt': '其他',
            'cx': '测站',
            'cbz': '泵站'
        }

        for key, type_name in station_types.items():
            if key in data and data[key]:
                for item in data[key]:
                    station_info = {
                        'station_name': item.get('zm', ''),           # 站名
                        'station_code': item.get('zh', ''),           # 站点编码
                        'station_type': type_name,                    # 站点类型
                        'station_category': item.get('zl', ''),       # 站点分类
                        'longitude': item.get('jd', ''),              # 经度
                        'latitude': item.get('wd', ''),               # 纬度
                        'water_level': item.get('z', ''),             # 水位
                        'flow_rate': item.get('q', ''),               # 流量
                        'storage': item.get('w', ''),                 # 蓄水量
                        'update_time': item.get('tm', ''),            # 更新时间
                        'area': item.get('xzqh', ''),                 # 行政区划
                        'river_name': item.get('shmc', ''),           # 河流名称
                        'alert_level': item.get('bxdj', ''),          # 报汛等级
                        'warning_water_level': item.get('jjsw', ''),  # 警戒水位
                        'flood_level': item.get('bzsw', ''),          # 保证水位
                        'status': item.get('zt', ''),                 # 状态
                    }
                    water_stations.append(station_info)

        print(f"解析完成，共获取 {len(water_stations)} 个水情站点")
        return water_stations

    def get_all_cities_water_info(self, station_types=None, alert_levels=None):
        """获取所有城市的水情信息"""
        all_water_info = []
        
        for city in self.area_map.keys():
            print(f"\n正在获取 {city} 的水情信息...")
            
            # 先获取该城市的总体信息
            water_info = self.get_water_info(
                city=city, 
                district='',  # 空字符串表示整个城市
                station_types=station_types,
                alert_levels=alert_levels
            )
            
            if water_info:
                all_water_info.extend(water_info)
            
            # 添加延迟避免请求过快
            time.sleep(1)
        
        return all_water_info

    def save_to_csv(self, water_data, filename=None):
        """保存水情数据到CSV文件"""
        if not water_data:
            print("没有水情数据可保存")
            return None
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"water_info_{timestamp}.csv"
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = [
                    'station_name', 'station_code', 'station_type', 'area',
                    'river_name', 'water_level', 'flow_rate', 'storage',
                    'alert_level', 'update_time', 'longitude', 'latitude',
                    'warning_water_level', 'flood_level', 'status'
                ]
                
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(water_data)
            
            print(f"CSV文件已保存: {filename}")
            return filename
            
        except Exception as e:
            print(f"保存CSV文件失败: {e}")
            return None

    def print_summary(self, water_data):
        """打印水情数据摘要"""
        if not water_data:
            print("没有水情数据")
            return
        
        print(f"\n水情数据摘要:")
        print(f"总计: {len(water_data)} 个水情站点")
        
        # 统计站点类型
        type_count = {}
        alert_count = {}
        area_count = {}
        
        for station in water_data:
            # 站点类型统计
            station_type = station.get('station_type', '未知')
            type_count[station_type] = type_count.get(station_type, 0) + 1
            
            # 报汛等级统计
            alert_level = station.get('alert_level', '未知')
            alert_count[alert_level] = alert_count.get(alert_level, 0) + 1
            
            # 区域统计
            area = station.get('area', '未知')
            area_count[area] = area_count.get(area, 0) + 1
        
        print(f"\n站点类型分布:")
        for stype, count in sorted(type_count.items(), key=lambda x: x[1], reverse=True):
            print(f"  {stype}: {count}个")
        
        print(f"\n报汛等级分布:")
        for level, count in sorted(alert_count.items(), key=lambda x: x[1], reverse=True):
            print(f"  等级{level}: {count}个")
        
        print(f"\n区域分布 (前10名):")
        for area, count in sorted(area_count.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  {area}: {count}个")

def main():
    """主函数"""
    print("浙江省水情信息爬虫启动")
    print("=" * 60)
    
    # 创建爬虫实例
    crawler = WaterInfoCrawler()
    
    # 示例1: 获取宁波市海曙区的水情信息
    print("示例1: 获取宁波市海曙区水情信息")
    water_info = crawler.get_water_info(
        city='宁波市',
        district='海曙区',
        station_types=['水库', '河道'],
        alert_levels=['1', '2', '3']
    )
    
    if water_info:
        crawler.print_summary(water_info)
        crawler.save_to_csv(water_info, "ningbo_haishui_water_info.csv")
    
    print("\n" + "=" * 60)
    
    # 示例2: 获取所有城市的水情信息
    print("示例2: 获取浙江省所有城市水情信息")
    all_water_info = crawler.get_all_cities_water_info(
        station_types=['水库', '河道'],
        alert_levels=['1', '2', '3', '4', '5']
    )
    
    if all_water_info:
        crawler.print_summary(all_water_info)
        crawler.save_to_csv(all_water_info, "zhejiang_all_water_info.csv")
    
    print("\n" + "=" * 60)
    print("水情信息获取完成！")

if __name__ == "__main__":
    main()
