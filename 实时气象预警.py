#!/usr/bin/env python3
# -*- coding: utf-8 -*-


import requests
import json
import re
import time
import os
from datetime import datetime, timedelta
from loguru import logger
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class WeatherWarningCrawler:
    def __init__(self, config):
        # 配置 loguru 日志
        logger.remove()  # 移除默认处理器
        logger.add(
            "weather_warning.log",
            rotation="1 day",
            retention="7 days",
            level="INFO",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
            encoding="utf-8"
        )
        logger.add(
            lambda msg: print(msg, end=""),
            level="INFO",
            format="{time:HH:mm:ss} | {level} | {message}\n"
        )

        # 从配置文件加载配置
        self.config = config
        self.time_range_start = config["time_range"]["start_hour"]
        self.time_range_end = config["time_range"]["end_hour"]
        self.interval_minutes = config["monitoring"]["interval_minutes"]

        # 微信配置
        self.wechat_config = config.get("wechat", {})
        self.wechat_url = self.wechat_config.get("api_url", "http://10.70.230.46:5003/robot_mothod/api/robot")
        self.wechat_wxid = self.wechat_config.get("wxid", "huabaifeng")

        self.base_url = "https://www.12379zj.cn"
        self.config_js_url = f"{self.base_url}/zjemw/resources/newClient/js/map/config/config.js"
        self.api_url = f"{self.base_url}/api/share"
        self.cache_file = "password_cache.json"
        self.cache_duration = 3600  # 1小时缓存

        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Origin': 'https://www.12379zj.cn',
            'Referer': 'https://www.12379zj.cn/map/index/toIndex'
        }
    
    def get_password_from_official(self):
        """从官方配置文件获取最新密码"""
        try:
            logger.info("正在从官方配置文件获取密码...")
            response = requests.get(self.config_js_url, timeout=10, verify=False)
            response.raise_for_status()

            js_content = response.text
            password_pattern = r"'apiPassword'\s*:\s*'([^']+)'"
            match = re.search(password_pattern, js_content)

            if match:
                password = match.group(1)
                logger.info(f"成功获取密码 (长度: {len(password)})")
                return password
            else:
                logger.error("未在配置文件中找到密码")
                return None

        except Exception as e:
            logger.error(f"获取配置文件失败: {e}")
            return None
    
    def load_cache(self):
        """加载缓存的密码"""
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache = json.load(f)

            cache_time = cache.get('timestamp', 0)
            if time.time() - cache_time < self.cache_duration:
                return cache.get('password')
            else:
                logger.info("缓存已过期")
                return None

        except FileNotFoundError:
            return None
        except Exception as e:
            logger.error(f"读取缓存失败: {e}")
            return None
    
    def save_cache(self, password):
        """保存密码到缓存"""
        try:
            cache = {
                'password': password,
                'timestamp': time.time(),
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"保存缓存失败: {e}")
    
    def test_password(self, password):
        """测试密码是否有效"""
        data = {
            'userCode': 'ZJWechat',
            'password': password,
            'token': '1212232323',
            'identifier': 'iden11323232',
            'dataformat': 'JSON',
            'interfaceCode': 'A0001',
            'params': '{"isChildrens":true,"alarmType":2}'
        }
        
        try:
            response = requests.post(self.api_url, headers=self.headers, data=data, timeout=10, verify=False)
            result = response.json()
            
            if result.get('returnCode') == '0':
                return True, result.get('total', 0)
            else:
                return False, result.get('returnMessage', '未知错误')
                
        except Exception as e:
            return False, str(e)
    
    def get_valid_password(self):
        """获取有效的密码"""
        # 1. 尝试缓存
        cached_password = self.load_cache()
        if cached_password:
            is_valid, _ = self.test_password(cached_password)
            if is_valid:
                return cached_password

        # 2. 从官方获取
        new_password = self.get_password_from_official()
        if new_password:
            is_valid, _ = self.test_password(new_password)
            if is_valid:
                self.save_cache(new_password)
                return new_password

        # 3. 备用密码
        fallback_password = "CifcIqVPix/S4rwY6Y91ZIeAS9sw7DPQstphN8v5iqYQ8dfv6dolHImGoUih9vPr0WPyb1wTHCCcghKnzmIlIQDeytofz5ixJx9HlBYluDN9K3tf43gCNjSS4VEqpvk6RZ+DNvsjTcCNaEn9/KNR+foUkI51FBqjV96xxbcNGC8="
        is_valid, _ = self.test_password(fallback_password)
        if is_valid:
            return fallback_password
        
        return None
    
    def get_warning_data(self, alarm_type=2, is_childrens=True, retry_count=3):
        """获取预警数据"""
        for attempt in range(retry_count):
            password = self.get_valid_password()

            if not password:
                logger.error(f"第{attempt+1}次尝试：无法获取有效密码")
                if attempt < retry_count - 1:
                    time.sleep(10)
                continue

            data = {
                'userCode': 'ZJWechat',
                'password': password,
                'token': '1212232323',
                'identifier': 'iden11323232',
                'dataformat': 'JSON',
                'interfaceCode': 'A0001',
                'params': json.dumps({
                    "isChildrens": is_childrens,
                    "alarmType": alarm_type
                })
            }

            try:
                logger.info(f"第{attempt+1}次尝试获取预警数据...")
                response = requests.post(self.api_url, headers=self.headers, data=data, timeout=30, verify=False)
                response.raise_for_status()

                result = response.json()

                if result.get('returnCode') == '0':
                    logger.info(f"成功获取数据，共 {result.get('total', 0)} 条预警信息")
                    return result
                else:
                    error_msg = result.get('returnMessage', '未知错误')
                    logger.error(f"API返回错误: {error_msg}")

                    if '密码' in error_msg or 'password' in error_msg.lower():
                        try:
                            os.remove(self.cache_file)
                        except:
                            pass

                        if attempt < retry_count - 1:
                            time.sleep(5)
                        continue
                    else:
                        return None

            except Exception as e:
                logger.error(f"请求失败: {e}")
                if attempt < retry_count - 1:
                    time.sleep(5)
                continue

        return None
    
    def parse_warning_data(self, data):
        """解析预警数据"""
        if not data or 'data' not in data:
            return []

        warnings = []
        for item in data['data']:
            alert = item.get('alert', {})
            info = alert.get('info', {})
            area = info.get('area', {})

            warning_info = {
                'title': info.get('headline', ''),
                'sender': alert.get('sender', ''),
                'time': alert.get('sendTime', ''),
                'level': info.get('severity', ''),
                'type': info.get('disaName', ''),
                'area': area.get('areaDesc', ''),
                'expires': info.get('expires', ''),
                'description': info.get('description', ''),
                'identifier': alert.get('identifier', ''),
                'instruction': info.get('instruction', ''),
                'sender_name': info.get('senderName', '')
            }
            warnings.append(warning_info)

        return warnings
    
    def is_in_time_range(self, warning_time):
        """检查预警时间是否在配置的时间范围内"""
        try:
            # 解析时间字符串，去掉时区信息
            time_str = warning_time.replace('+08:00', '').strip()
            warning_dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
            warning_hour = warning_dt.hour

            # 检查是否在时间范围内
            if self.time_range_start <= self.time_range_end:
                # 正常范围，如 8-18
                return self.time_range_start <= warning_hour < self.time_range_end
            else:
                # 跨天范围，如 22-6
                return warning_hour >= self.time_range_start or warning_hour < self.time_range_end
        except Exception as e:
            logger.error(f"解析时间失败: {e}")
            return True  # 解析失败时默认包含

    def group_warnings_by_type(self, warnings):
        """按预警类型分组并排序"""
        # 过滤时间范围
        filtered_warnings = []
        for warning in warnings:
            if self.is_in_time_range(warning['time']):
                filtered_warnings.append(warning)

        if not filtered_warnings:
            return {}

        # 按类型分组
        grouped = {}
        for warning in filtered_warnings:
            warning_type = warning['type']
            if warning_type not in grouped:
                grouped[warning_type] = []
            grouped[warning_type].append(warning)

        # 每组内按等级排序（红橙黄蓝）
        level_order = {'Red': 1, 'Orange': 2, 'Yellow': 3, 'Blue': 4, 'Unknown': 5}
        for warning_type in grouped:
            grouped[warning_type].sort(key=lambda x: level_order.get(x['level'], 5))

        return grouped

    def print_simple_text(self, warnings):
        """输出纯文本简化格式"""
        grouped_warnings = self.group_warnings_by_type(warnings)

        if not grouped_warnings:
            logger.info("当前无预警信息!")
            return

        # 输出每个类型的预警信息
        for warning_type, type_warnings in grouped_warnings.items():
            logger.info(f"当前【{warning_type}】预警信息: (共{len(type_warnings)}条)")

            for i, warning in enumerate(type_warnings, 1):
                # 获取预警等级的中文名称
                level_map = {
                    'Red': '红色',
                    'Orange': '橙色',
                    'Yellow': '黄色',
                    'Blue': '蓝色',
                    'Unknown': 'Unknown'
                }
                level_cn = level_map.get(warning['level'], warning['level'])

                # 格式化发布时间，去掉时区信息
                publish_time = warning['time'].replace('+08:00', '').strip()

                logger.info(f"{i}.{level_cn} {warning_type} {warning['sender']} {publish_time}")

            logger.info("")  # 每个类型之间空一行

    def send_wechat_message(self, content):
        """发送微信消息 - 已注释"""
        # 微信通知功能已注释掉
        logger.info("微信通知功能已禁用")
        return True, "微信通知已禁用"
        # try:
        #     data = {
        #         'wxid': self.wechat_wxid,   # 52366126463@chatroom
        #         'data_type': 'Text',
        #         'data': content
        #     }

        #     res = requests.post(self.wechat_url, data=data, timeout=10)
        #     result = res.json()
        #     return True, result
        # except Exception as e:
        #     logger.error(f"发送微信消息失败: {e}")
        #     return False, str(e)

    def format_warnings_for_wechat(self, warnings):
        """格式化预警信息为微信消息内容，返回按类型分组的消息列表"""
        grouped_warnings = self.group_warnings_by_type(warnings)

        if not grouped_warnings:
            return ["当前无预警信息!"]

        # 为每个预警类型构建单独的消息
        messages = []

        for warning_type, type_warnings in grouped_warnings.items():
            message_lines = []
            message_lines.append(f"当前【{warning_type}】预警信息: (共{len(type_warnings)}条)")

            for i, warning in enumerate(type_warnings, 1):
                # 获取预警等级的中文名称
                level_map = {
                    'Red': '红色',
                    'Orange': '橙色',
                    'Yellow': '黄色',
                    'Blue': '蓝色',
                    'Unknown': 'Unknown'
                }
                level_cn = level_map.get(warning['level'], warning['level'])

                # 格式化发布时间，去掉时区信息
                publish_time = warning['time'].replace('+08:00', '').strip()

                message_lines.append(f"{i}.{level_cn} {warning_type} {warning['sender']} {publish_time}")

            messages.append("\n".join(message_lines))

        return messages

def continuous_monitor_with_schedule(config):
    """智能时间调度监控：启动时执行一次，然后在下个整点执行，之后按间隔执行"""
    logger.info("浙江省预警信息连续监控系统")
    logger.info("=" * 60)

    crawler = WeatherWarningCrawler(config)

    # 从配置获取监控参数
    interval_minutes = crawler.interval_minutes

    current_time = datetime.now()
    logger.info(f"\n监控设置：")
    logger.info(f"监控间隔：{interval_minutes}分钟")
    logger.info(f"时间范围：{crawler.time_range_start:02d}:00-{crawler.time_range_end:02d}:00")
    logger.info(f"启动时间：{current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("\n开始监控...")
    logger.info("按 Ctrl+C 停止监控")

    def execute_monitoring():
        """执行一次监控"""
        current_time = datetime.now()
        logger.info(f"\n{'='*80}")
        logger.info(f"监控时间：{current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"{'='*80}")

        # 获取预警信息
        raw_data = crawler.get_warning_data()
        if raw_data:
            warnings = crawler.parse_warning_data(raw_data)

            # 控制台输出
            logger.info("")
            crawler.print_simple_text(warnings)

            # 微信消息发送功能已注释
            logger.info(f"\n微信通知功能已禁用，仅显示控制台输出")
            # wechat_messages = crawler.format_warnings_for_wechat(warnings)
            # logger.info(f"\n正在发送微信消息，共{len(wechat_messages)}条...")

            # success_count = 0
            # for i, message in enumerate(wechat_messages, 1):
            #     logger.info(f"发送第{i}条消息...")
            #     success, result = crawler.send_wechat_message(message)
            #     if success:
            #         success_count += 1
            #         logger.info(f"第{i}条消息发送成功")
            #     else:
            #         logger.warning(f"第{i}条消息发送失败: {result}")

            #     # 消息间隔，避免发送过快
            #     if i < len(wechat_messages):
            #         time.sleep(1)

            # logger.info(f"微信消息发送完成: {success_count}/{len(wechat_messages)} 条成功")
            # if success_count == 0:
            #     logger.warning(f"所有微信消息发送失败，但程序继续运行")
            #     logger.info(f"建议检查网络连接和API配置")
        else:
            logger.error("未获取到预警信息")

    try:
        # 第一次：启动时立即执行
        execute_monitoring()

        # 计算下个对齐时间点
        current_time = datetime.now()
        current_minute = current_time.minute

        # 根据间隔时间计算下个对齐的分钟数
        if interval_minutes == 60:
            # 60分钟间隔：对齐到整点
            next_minute = 0
            next_hour = current_time.hour + 1
            next_time = current_time.replace(hour=next_hour, minute=0, second=0, microsecond=0)
            if next_hour >= 24:
                next_time = (current_time + timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
        else:
            # 其他间隔：对齐到相应的分钟倍数
            next_minute = ((current_minute // interval_minutes) + 1) * interval_minutes
            if next_minute >= 60:
                # 跨小时
                next_hour = current_time.hour + 1
                next_minute = 0
                next_time = current_time.replace(hour=next_hour, minute=next_minute, second=0, microsecond=0)
                if next_hour >= 24:
                    next_time = (current_time + timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
            else:
                # 同一小时内
                next_time = current_time.replace(minute=next_minute, second=0, microsecond=0)

        # 等待到下个对齐时间点
        wait_seconds = (next_time - current_time).total_seconds()
        interval_desc = f"整{interval_minutes}分钟" if interval_minutes < 60 else "整点"
        logger.info(f"\n下次监控时间：{next_time.strftime('%H:%M:%S')} (下个{interval_desc})")
        logger.info(f"等待 {int(wait_seconds//60)}分{int(wait_seconds%60)}秒...")
        time.sleep(wait_seconds)

        # 第二次：在对齐时间点执行
        execute_monitoring()

        # 后续：按间隔时间执行
        while True:
            current_time = datetime.now()
            next_time = current_time + timedelta(minutes=interval_minutes)
            logger.info(f"\n下次监控时间：{next_time.strftime('%H:%M:%S')}")
            time.sleep(interval_minutes * 60)

            execute_monitoring()

    except KeyboardInterrupt:
        logger.info(f"\n\n监控已停止")
        logger.info(f"停止时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except FileNotFoundError:
        logger.warning("配置文件 config.json 不存在，使用默认配置")
        return {
            "time_range": {"start_hour": 7, "end_hour": 19},
            "monitoring": {"interval_minutes": 60}
        }
    except Exception as e:
        logger.error(f"读取配置文件失败: {e}，使用默认配置")
        return {
            "time_range": {"start_hour": 7, "end_hour": 19},
            "monitoring": {"interval_minutes": 60},
            "wechat": {
                "api_url": "http://10.70.230.46:5003/robot_mothod/api/robot",
                "wxid": "52366126463@chatroom"
            }
        }

def test_single_run():
    """测试单次运行 - 只获取一次数据"""
    # 加载配置
    config = load_config()

    logger.info("浙江省预警信息单次测试")
    logger.info("=" * 60)
    logger.info(f"微信通知功能已禁用，仅显示控制台输出")
    logger.info("=" * 60)

    crawler = WeatherWarningCrawler(config)

    # 获取预警信息
    raw_data = crawler.get_warning_data()
    if raw_data:
        warnings = crawler.parse_warning_data(raw_data)

        logger.info(f"\n📊 获取结果:")
        logger.info(f"总预警数量: {len(warnings)} 条")

        # 按类型统计
        type_count = {}
        level_count = {}
        for warning in warnings:
            warning_type = warning['type']
            warning_level = warning['level']
            type_count[warning_type] = type_count.get(warning_type, 0) + 1
            level_count[warning_level] = level_count.get(warning_level, 0) + 1

        logger.info(f"\n预警类型分布:")
        for wtype, count in sorted(type_count.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"  {wtype}: {count}条")

        logger.info(f"\n预警等级分布:")
        for level, count in sorted(level_count.items(), key=lambda x: x[1], reverse=True):
            level_cn = {'Red': '红色', 'Orange': '橙色', 'Yellow': '黄色', 'Blue': '蓝色'}.get(level, level)
            logger.info(f"  {level_cn}: {count}条")

        # 控制台输出详细信息
        logger.info(f"\n详细预警信息:")
        crawler.print_simple_text(warnings)

    else:
        logger.error("未获取到预警信息")

def main():
    """主函数 - 选择运行模式"""
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # 测试模式：只运行一次
        test_single_run()
    else:
        # 正常模式：连续监控
        config = load_config()
        logger.info(f"配置加载完成：")
        logger.info(f"  时间范围: {config['time_range']['start_hour']:02d}:00-{config['time_range']['end_hour']:02d}:00")
        logger.info(f"  监控间隔: {config['monitoring']['interval_minutes']}分钟")
        logger.info(f"  微信通知: 已禁用")
        continuous_monitor_with_schedule(config)

if __name__ == "__main__":
    main()
