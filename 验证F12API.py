#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_f12_api():
    """直接测试F12发现的API"""
    
    # F12发现的实时API
    url = "https://sqfb.slt.zj.gov.cn/rest/newList/getNewDataListRealTime.jsp"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Referer': 'https://sqfb.slt.zj.gov.cn/winindex.html'
    }
    
    # 测试永嘉县参数
    params = {
        'areaFlag': '1',
        'sss': '温州市',
        'ssx': '永嘉县',
        'zl': 'RR,',  # 水库
        'sfcj': '1'   # 超限
    }
    
    print("🔍 测试F12发现的实时API")
    print(f"URL: {url}")
    print(f"参数: {params}")
    print("="*50)
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=30, verify=False)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ 成功解析JSON")
                print(f"数据类型: {type(data)}")
                
                if isinstance(data, dict):
                    print(f"顶级字段: {list(data.keys())}")
                    
                    # 检查每个字段
                    for key, value in data.items():
                        print(f"\n字段 '{key}':")
                        print(f"  类型: {type(value)}")
                        
                        if isinstance(value, list):
                            print(f"  数组长度: {len(value)}")
                            if value:
                                print(f"  第一项: {value[0]}")
                                if isinstance(value[0], dict):
                                    print(f"  第一项字段: {list(value[0].keys())}")
                        else:
                            print(f"  值: {value}")
                            
                else:
                    print(f"数据: {data}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"响应内容: {response.text[:500]}")
                
        else:
            print(f"❌ 请求失败")
            print(f"响应内容: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_f12_api()
