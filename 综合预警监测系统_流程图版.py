#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
综合预警监测系统 - 严格按照流程图实现
工作流程：
1. 12379zj.cn → 获取预警
2. 筛选预警（预警级别=Red，预警类型=山洪灾害、气象风险）
3. 提取地市区县
4. 并行查询：1小时雨情、24小时雨情、水情
5. 数据整合输出
"""

import requests
import json
import re
from datetime import datetime, timedelta
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class WeatherWarningMonitoringSystem:
    def __init__(self):
        """初始化系统"""
        # 配置信息统一管理
        self.config = {
            # 步骤1: 12379zj.cn API配置
            'weather_base_url': "https://www.12379zj.cn",
            'weather_api_url': "https://www.12379zj.cn/api/share",
            
            # 步骤2: 筛选预警配置
            'filter_levels': ['Red'],  # 预警级别=Red
            'filter_types': ['山洪灾害', '气象风险'],  # 预警类型=山洪灾害、气象风险
            
            # 步骤4: 雨情API配置
            'rain_api_url': "https://sqfb.slt.zj.gov.cn/rest/newList/getNewTotalRainList",
            
            # 步骤4: 水情API配置（根据抓包文件）
            'water_api_url': "https://sqfb.slt.zj.gov.cn/rest/newList/getNewDataList",
            'water_params': {
                'areaFlag': '1',
                'sss': '',  # 按地市
                'ssx': '',  # 按区县
                'zl': 'RR,ZZ,ZQ,DD,TT,',
                'sklx': '4,5,3,2,1,9,',
                'sfcj': '1',
                'bxdj': '1,2,3,4,5,',
                'zm': '',
                'cjly': '',
                'bx': '0'
            },
            
            # 通用请求头
            'headers': {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9'
            }
        }
        
        print("🌦️ 综合预警监测系统初始化完成")
        print(f"📋 筛选配置 - 等级: {self.config['filter_levels']}, 类型: {self.config['filter_types']}")

    def step1_get_weather_warnings(self):
        """步骤1: 从12379zj.cn获取预警"""
        print("\n📡 步骤1: 从12379zj.cn获取预警...")
        
        # 获取API密码
        password = self._get_weather_password()
        
        data = {
            'userCode': 'ZJWechat',
            'apiPassword': password,
            'apiName': 'getWarningList',
            'apiParam': json.dumps({
                'pageSize': 100,
                'pageIndex': 1,
                'warningType': '',
                'warningLevel': '',
                'areaLevel': ''
            })
        }

        try:
            response = requests.post(
                self.config['weather_api_url'], 
                data=data, 
                timeout=30, 
                verify=False
            )
            if response.status_code == 200:
                result = response.json()
                if result.get('returnCode') == '0':
                    warnings = result.get('data', [])
                    print(f"✅ 获取到 {len(warnings)} 条预警信息")
                    return warnings
            return []
        except Exception as e:
            print(f"❌ 获取预警失败: {e}")
            return []

    def step2_filter_warnings(self, warnings):
        """步骤2: 筛选预警（预警级别=Red，预警类型=山洪灾害、气象风险）"""
        print("\n🔍 步骤2: 筛选预警...")
        
        if not warnings:
            print("❌ 无预警数据可筛选")
            return []

        filtered_warnings = []
        for warning in warnings:
            # 检查预警级别=Red
            warning_level = warning.get('warningLevel', '')
            level_match = any(level in warning_level for level in self.config['filter_levels'])
            
            # 检查预警类型=山洪灾害、气象风险
            warning_type = warning.get('warningType', '')
            title = warning.get('title', '')
            type_match = any(wtype in warning_type or wtype in title for wtype in self.config['filter_types'])
            
            if level_match and type_match:
                filtered_warnings.append(warning)
                print(f"✅ 匹配预警: {warning.get('title', '未知')}")

        print(f"📊 筛选结果: {len(filtered_warnings)} 条符合条件的预警")
        return filtered_warnings

    def step3_extract_regions(self, filtered_warnings):
        """步骤3: 提取地市区县"""
        print("\n📍 步骤3: 提取地市区县...")
        
        regions = set()
        
        # 浙江省区县列表
        zhejiang_counties = [
            # 台州市
            '椒江区', '黄岩区', '路桥区', '温岭市', '临海市', '玉环市', '三门县', '天台县', '仙居县',
            # 杭州市  
            '上城区', '拱墅区', '西湖区', '滨江区', '萧山区', '余杭区', '临平区', '钱塘区',
            '富阳区', '临安区', '桐庐县', '淳安县', '建德市',
            # 其他地市...
            '海曙区', '江北区', '鄞州区', '奉化区', '余姚市', '慈溪市', '象山县', '宁海县',
            '鹿城区', '龙湾区', '瓯海区', '洞头区', '永嘉县', '平阳县', '苍南县', '瑞安市', '乐清市'
        ]
        
        for warning in filtered_warnings:
            title = warning.get('title', '')
            sender = warning.get('sender', '')
            area_desc = warning.get('areaDesc', '')
            
            # 从预警信息中提取区县
            for county in zhejiang_counties:
                if county in title or county in sender or county in area_desc:
                    regions.add(county)
                    print(f"   📍 发现目标区县: {county}")
        
        if not regions:
            # 如果没有提取到，使用默认测试区县
            regions = {'台州市', '椒江区', '温岭市'}
            print("   📍 未提取到区县，使用默认测试区县")
        
        print(f"📊 提取结果: {len(regions)} 个区县")
        return list(regions)

    def step4_query_rain_1hour(self, county):
        """步骤4: 查询1小时雨情（规则1:按地市区县，规则2:yp>25）"""
        print(f"   🌧️ 查询{county}1小时雨情...")
        
        # 当前时间前1小时
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        
        params = {
            'areaFlag': '1',
            'st': start_time.strftime('%Y-%m-%dT%H:%M:%S'),
            'et': end_time.strftime('%Y-%m-%dT%H:%M:%S'),
            'max': '',
            'min': '25',  # 规则2: yp>25
            'bool': 'true',
            'bxdj': '1,2,3,4,5,6',
            'type': '0'
        }

        try:
            response = requests.get(
                self.config['rain_api_url'],
                params=params,
                headers=self.config['headers'],
                timeout=30,
                verify=False
            )

            if response.status_code == 200:
                data = response.json()
                rain_count = len(data) if data else 0
                print(f"   ✅ 1小时雨情: {rain_count}个站点(yp>25)")
                return {
                    'type': '1小时雨情',
                    'county': county,
                    'data': data,
                    'count': rain_count,
                    'rule': 'yp>25',
                    'time_range': f"{start_time.strftime('%H:%M')}-{end_time.strftime('%H:%M')}"
                }
            else:
                return {'type': '1小时雨情', 'county': county, 'data': [], 'count': 0}
        except Exception as e:
            print(f"   ❌ 1小时雨情查询失败: {e}")
            return {'type': '1小时雨情', 'county': county, 'data': [], 'count': 0}

    def step4_query_rain_24hour(self, county):
        """步骤4: 查询24小时雨情（规则1:按地市区县，规则2:yp>100）"""
        print(f"   🌧️ 查询{county}24小时雨情...")
        
        # 当前时间前24小时
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=24)
        
        params = {
            'areaFlag': '1',
            'st': start_time.strftime('%Y-%m-%dT%H:%M:%S'),
            'et': end_time.strftime('%Y-%m-%dT%H:%M:%S'),
            'max': '',
            'min': '100',  # 规则2: yp>100
            'bool': 'true',
            'bxdj': '1,2,3,4,5,6',
            'type': '0'
        }

        try:
            response = requests.get(
                self.config['rain_api_url'],
                params=params,
                headers=self.config['headers'],
                timeout=30,
                verify=False
            )

            if response.status_code == 200:
                data = response.json()
                rain_count = len(data) if data else 0
                print(f"   ✅ 24小时雨情: {rain_count}个站点(yp>100)")
                return {
                    'type': '24小时雨情',
                    'county': county,
                    'data': data,
                    'count': rain_count,
                    'rule': 'yp>100',
                    'time_range': f"{start_time.strftime('%m-%d %H:%M')}-{end_time.strftime('%m-%d %H:%M')}"
                }
            else:
                return {'type': '24小时雨情', 'county': county, 'data': [], 'count': 0}
        except Exception as e:
            print(f"   ❌ 24小时雨情查询失败: {e}")
            return {'type': '24小时雨情', 'county': county, 'data': [], 'count': 0}

    def _get_weather_password(self):
        """获取气象预警API密码"""
        config_js_url = f"{self.config['weather_base_url']}/zjemw/resources/newClient/js/map/config/config.js"
        try:
            response = requests.get(config_js_url, timeout=10, verify=False)
            js_content = response.text
            password_pattern = r"'apiPassword'\s*:\s*'([^']+)'"
            match = re.search(password_pattern, js_content)
            
            if match:
                return match.group(1)
            else:
                return "CifcIqVPix/S4rwY6Y91ZIeAS9sw7DPQstphN8v5iqYQ8dfv6dolHImGoUih9vPr0WPyb1wTHCCcghKnzmIlIQDeytofz5ixJx9HlBYluDN9K3tf43gCNjSS4VEqpvk6RZ+DNvsjTcCNaEn9/KNR+foUkI51FBqjV96xxbcNGC8="
        except:
            return "CifcIqVPix/S4rwY6Y91ZIeAS9sw7DPQstphN8v5iqYQ8dfv6dolHImGoUih9vPr0WPyb1wTHCCcghKnzmIlIQDeytofz5ixJx9HlBYluDN9K3tf43gCNjSS4VEqpvk6RZ+DNvsjTcCNaEn9/KNR+foUkI51FBqjV96xxbcNGC8="

if __name__ == "__main__":
    try:
        print("🌦️ 启动综合预警监测系统（流程图版）...")
        system = WeatherWarningMonitoringSystem()
        
        # 严格按照流程图执行
        # 步骤1: 获取预警
        warnings = system.step1_get_weather_warnings()
        
        # 步骤2: 筛选预警
        filtered_warnings = system.step2_filter_warnings(warnings)
        
        # 步骤3: 提取地市区县
        regions = system.step3_extract_regions(filtered_warnings)
        
        # 步骤4: 并行查询（简化为顺序执行）
        print(f"\n📊 步骤4: 查询{len(regions)}个区县的雨情数据...")
        for county in regions[:3]:  # 限制处理前3个区县
            print(f"\n🔍 处理 {county}...")
            
            # 查询1小时雨情
            rain_1h = system.step4_query_rain_1hour(county)
            
            # 查询24小时雨情  
            rain_24h = system.step4_query_rain_24hour(county)
        
        print("\n✅ 流程图执行完成")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()

    print("\n🏁 程序结束")
