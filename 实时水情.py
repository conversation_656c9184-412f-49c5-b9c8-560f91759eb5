#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
浙江省水情信息
"""

import requests
import csv
import time
from datetime import datetime

# ============================================
# 配置参数 - 集中管理
# ============================================

class WaterCrawlerConfig:
    """集中管理所有参数"""

    # API配置
    BASE_URL = "https://sqfb.slt.zj.gov.cn/rest/newList/getNewDataList"

    # 请求头配置
    HEADERS = {
        'Host': 'sqfb.slt.zj.gov.cn',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Referer': 'https://sqfb.slt.zj.gov.cn/weIndex.html',
        'Accept-Language': 'zh-CN,zh;q=0.9'
    }

    # 默认查询参数 - 可在这里修改默认值
    DEFAULT_QUERY = {
        'area_flag': '行政区划',           # 区域标志: 行政区划/流域
        'city': '宁波市',                 # 城市: 浙江省或11地市
        'district': '海曙区',                   # 区县: 具体区县名称
        'station_categories': ['水库'],    # 站点类别: 水库/河道/堰闸/潮汐
        'reservoir_types': ['所有水库'],   # 水库类型: 大型/中型/小一/其他/所有
        'exceed_limit': '超限',           # 超限筛选: 超限/所有
        'alert_levels': ['1', '2', '3', '4', '5']  # 报汛等级: 1-5
    }

    # 区域标志映射
    AREA_FLAG_MAP = {
        '行政区划': '1',
        '流域': '2'
    }

    # 浙江省11地市
    CITIES = [
        '浙江省', '杭州市', '宁波市', '温州市', '嘉兴市',
        '湖州市', '绍兴市', '金华市', '衢州市', '舟山市',
        '台州市', '丽水市'
    ]

    # 站点类别映射
    STATION_CATEGORY_MAP = {
        '水库': 'RR,',
        '河道': 'ZZ,ZQ,',
        '堰闸': 'DD,',
        '潮汐': 'TT,'
    }

    # 水库类型映射
    RESERVOIR_TYPE_MAP = {
        '大型水库': '4,5,',
        '中型水库': '3,',
        '小一型水库': '2,',
        '其他水库': '1,9,',  # 含小二型
        '所有水库': '4,5,3,2,1,9,'
    }

    # 是否超限映射
    EXCEED_LIMIT_MAP = {
        '超限': '1',
        '所有': '0'
    }

    # 超限状态说明
    STATUS_DESCRIPTION = {
        'cx': '超限',      # 超过限制水位
        'cjj': '超警戒',   # 超过警戒水位
        'cbz': '超保证',   # 超过保证水位
        'qt': '正常'       # 未超限，水位正常
    }

class AccurateWaterCrawler:
    def __init__(self, config=None):
        """
        初始化水情爬虫

        Args:
            config: 配置对象，默认使用 WaterCrawlerConfig
        """
        self.config = config or WaterCrawlerConfig()

        # 从配置类获取参数
        self.base_url = self.config.BASE_URL
        self.headers = self.config.HEADERS.copy()
        self.area_flag_map = self.config.AREA_FLAG_MAP
        self.cities = self.config.CITIES
        self.station_category_map = self.config.STATION_CATEGORY_MAP
        self.reservoir_type_map = self.config.RESERVOIR_TYPE_MAP
        self.exceed_limit_map = self.config.EXCEED_LIMIT_MAP

    def get_water_info(self, area_flag=None, city=None, district=None,
                      station_categories=None, reservoir_types=None,
                      exceed_limit=None, alert_levels=None):
        """
        获取水情信息

        Args:
            area_flag: 区域标志 ('行政区划' 或 '流域')，默认从配置获取
            city: 城市名称 (如 '宁波市', '浙江省')，默认从配置获取
            district: 区县名称 (如 '海曙区')，默认从配置获取
            station_categories: 站点类别列表 (如 ['水库', '河道'])，默认从配置获取
            reservoir_types: 水库类型列表 (如 ['大型水库', '中型水库'])，默认从配置获取
            exceed_limit: 是否超限 ('超限' 或 '所有')，默认从配置获取
            alert_levels: 报汛等级列表 (如 ['1', '2', '3'])，默认从配置获取
        """

        # 使用配置的默认值
        area_flag = area_flag or self.config.DEFAULT_QUERY['area_flag']
        city = city or self.config.DEFAULT_QUERY['city']
        district = district or self.config.DEFAULT_QUERY['district']
        station_categories = station_categories or self.config.DEFAULT_QUERY['station_categories']
        reservoir_types = reservoir_types or self.config.DEFAULT_QUERY['reservoir_types']
        exceed_limit = exceed_limit or self.config.DEFAULT_QUERY['exceed_limit']
        alert_levels = alert_levels or self.config.DEFAULT_QUERY['alert_levels']
        
        # 构建站点类别参数
        zl_codes = []
        for category in station_categories:
            if category in self.station_category_map:
                zl_codes.append(self.station_category_map[category])
        zl_param = ''.join(zl_codes)
        
        # 构建水库类型参数
        sklx_codes = []
        for res_type in reservoir_types:
            if res_type in self.reservoir_type_map:
                sklx_codes.append(self.reservoir_type_map[res_type])
        sklx_param = ''.join(sklx_codes)
        
        # 构建请求参数
        params = {
            'areaFlag': self.area_flag_map.get(area_flag, '1'),
            'sss': city,
            'ssx': district,
            'zl': zl_param,
            'sklx': sklx_param,
            'ly': '',
            'sfcj': self.exceed_limit_map.get(exceed_limit, '0'),
            'bxdj': ','.join(alert_levels) + ',',
            'zm': '',
            'cjly': '',
            'bx': '0'
        }
        
        try:
            print(f"正在获取水情信息...")
            print(f"区域: {area_flag} - {city} {district}")
            print(f"站点类别: {station_categories}")
            print(f"水库类型: {reservoir_types}")
            print(f"超限筛选: {exceed_limit}")
            
            response = requests.get(
                self.base_url,
                params=params,
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"获取成功！")
                return self.parse_water_data(data)
            else:
                print(f"请求失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"请求异常: {e}")
            return None

    def parse_water_data(self, data):
        """解析水情数据"""
        if not data:
            print("无数据")
            return []
        
        water_stations = []
        
        # 使用配置中的状态说明
        station_types = self.config.STATUS_DESCRIPTION
        
        for key, type_name in station_types.items():
            if key in data and data[key]:
                for item in data[key]:
                    station_info = {
                        'station_name': item.get('zm', ''),           # 站名
                        'station_code': item.get('zh', ''),           # 站点编码
                        'station_type': type_name,                    # 站点类型
                        'station_category': item.get('zl', ''),       # 站点分类
                        'longitude': item.get('jd', ''),              # 经度
                        'latitude': item.get('wd', ''),               # 纬度
                        'raw_data': item                              # 原始数据
                    }
                    water_stations.append(station_info)
        
        print(f"解析完成，共获取 {len(water_stations)} 个水情站点")
        return water_stations

    def get_all_cities_water_info(self, station_categories=None, reservoir_types=None):
        """获取所有城市的水情信息"""
        all_water_info = []
        
        for city in self.cities:
            print(f"\n正在获取 {city} 的水情信息...")
            
            water_info = self.get_water_info(
                city=city,
                district='',
                station_categories=station_categories,
                reservoir_types=reservoir_types
            )
            
            if water_info:
                all_water_info.extend(water_info)
            
            time.sleep(1)  # 添加延迟
        
        return all_water_info

    def save_to_csv(self, water_data, filename=None):
        """保存水情数据到CSV文件"""
        if not water_data:
            print("没有水情数据可保存")
            return None
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"water_info_{timestamp}.csv"
        
        try:
            fieldnames = [
                'station_name', 'station_code', 'station_type', 
                'station_category', 'longitude', 'latitude'
            ]
            
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for station in water_data:
                    row = {key: station.get(key, '') for key in fieldnames}
                    writer.writerow(row)
            
            print(f"CSV文件已保存: {filename}")
            return filename
            
        except Exception as e:
            print(f"保存CSV文件失败: {e}")
            return None

    def print_data(self, water_data):
        """打印水情数据"""
        if not water_data:
            print("没有水情数据")
            return

        print(f"获取到 {len(water_data)} 个水情站点:")
        print("=" * 60)

        for i, station in enumerate(water_data, 1):
            print(f"{i}. 站点: {station.get('station_name', '未知')}")
            print(f"   编码: {station.get('station_code', 'N/A')}")
            print(f"   状态: {station.get('station_type', 'N/A')}")
            print(f"   分类: {station.get('station_category', 'N/A')}")
            print(f"   坐标: {station.get('longitude', 'N/A')}, {station.get('latitude', 'N/A')}")

            # 显示原始数据的关键字段
            raw_data = station.get('raw_data', {})
            if raw_data:
                print(f"   详细: {raw_data}")

            print("-" * 60)

def main():
    print("浙江省水情信息查询")
    print("=" * 50)

    # 创建爬虫实例
    crawler = AccurateWaterCrawler()

    # 获取水情数据
    water_info = crawler.get_water_info()

    if water_info:
        # 打印获取到的数据
        crawler.print_data(water_info)

        # 保存到CSV
        crawler.save_to_csv(water_info, "water_data.csv")
        print(f"\n数据已保存到: water_data.csv")
    else:
        print("未获取到水情数据")

if __name__ == "__main__":
    main()
