# 浙江省实时雨情数据获取工具

这是一个用于获取浙江省实时雨情数据的Python工具，通过调用浙江省水利厅的公开API接口来获取降雨量数据。

## 功能特点

- 🌧️ 获取实时雨情数据
- 📊 支持自定义时间范围查询
- 💾 数据保存为JSON格式
- 🔧 灵活的参数配置
- 📈 数据摘要显示

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本使用

直接运行主程序：

```bash
python main.py
```

程序将自动获取过去24小时的雨情数据。

### 高级使用

你也可以在代码中导入并使用相关函数：

```python
from main import get_rainfall_data, display_rainfall_summary, save_to_file

# 获取指定时间范围的数据
data = get_rainfall_data(
    start_time='2025-01-15T08:00:00',
    end_time='2025-01-15T20:00:00'
)

# 显示数据摘要
display_rainfall_summary(data)

# 保存数据到文件
save_to_file(data, 'my_rainfall_data.json')
```

## 参数说明

`get_rainfall_data()` 函数支持以下参数：

- `start_time`: 开始时间，格式为 'YYYY-MM-DDTHH:MM:SS'，默认为当前时间前24小时
- `end_time`: 结束时间，格式为 'YYYY-MM-DDTHH:MM:SS'，默认为当前时间
- `area_flag`: 区域标志，默认为1
- `min_rain`: 最小降雨量，默认为0
- `max_rain`: 最大降雨量，默认为空
- `warning_levels`: 预警等级列表，默认为[1,2,3,4,5,6]
- `data_type`: 数据类型，默认为0

## API接口说明

本工具使用的API接口：
```
https://sqfb.slt.zj.gov.cn/rest/newList/getNewTotalRainList
```

接口参数：
- `areaFlag`: 区域标志
- `st`: 开始时间
- `et`: 结束时间
- `max`: 最大降雨量
- `min`: 最小降雨量
- `bool`: 布尔值，固定为true
- `bxdj`: 预警等级，多个等级用逗号分隔
- `type`: 数据类型

## 输出格式

程序会显示获取到的数据摘要，并可选择将完整数据保存为JSON文件。保存的文件名格式为：`rainfall_data_YYYYMMDD_HHMMSS.json`

## 注意事项

1. 确保网络连接正常
2. API接口可能有访问频率限制
3. 时间格式必须严格按照 'YYYY-MM-DDTHH:MM:SS' 格式
4. 数据的可用性取决于浙江省水利厅的API服务状态

## 错误处理

程序包含完善的错误处理机制：
- 网络请求错误
- JSON解析错误
- 文件保存错误
- 其他未知错误

## 许可证

本项目仅供学习和研究使用，请遵守相关API的使用条款。