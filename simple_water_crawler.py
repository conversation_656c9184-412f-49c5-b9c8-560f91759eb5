#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
浙江省水情信息爬虫 - 简化版
快速获取实时水情数据
"""

import requests
import json
from datetime import datetime

def get_water_info(city='宁波市', district='海曙区'):
    """
    获取指定地区的水情信息
    
    Args:
        city: 城市名称 (如: '宁波市', '杭州市')
        district: 区县名称 (如: '海曙区', '西湖区')
    
    Returns:
        dict: 水情数据
    """
    
    url = "https://sqfb.slt.zj.gov.cn/rest/newList/getNewDataList"
    
    # 请求头
    headers = {
        'Host': 'sqfb.slt.zj.gov.cn',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Referer': 'https://sqfb.slt.zj.gov.cn/weIndex.html',
        'Accept-Language': 'zh-CN,zh;q=0.9'
    }
    
    # 请求参数 (基于抓包分析)
    params = {
        'areaFlag': '1',        # 区域标志
        'sss': city,            # 省市
        'ssx': district,        # 区县  
        'zl': 'RR,',           # 站点类别
        'sklx': '4,5,3,2,1,9,', # 站点类型 (水库,河道,湖泊,闸坝,泵站,其他)
        'ly': '',              # 来源
        'sfcj': '1',           # 是否采集
        'bxdj': '1,2,3,4,5,',  # 报汛等级
        'zm': '',              # 站名
        'cjly': '',            # 采集来源
        'bx': '0'              # 报汛
    }
    
    try:
        print(f"正在获取 {city} {district} 的水情信息...")
        
        response = requests.get(url, params=params, headers=headers, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"获取成功！")
            return data
        else:
            print(f"请求失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"请求异常: {e}")
        return None

def parse_and_display(data):
    """解析并显示水情数据"""
    if not data:
        print("无水情数据")
        return

    # 合并所有类型的站点数据
    all_stations = []
    station_types = {
        'cjj': '采集井',
        'qt': '其他',
        'cx': '测站',
        'cbz': '泵站'
    }

    for key, type_name in station_types.items():
        if key in data and data[key]:
            for station in data[key]:
                station['station_type_name'] = type_name
                all_stations.append(station)

    if not all_stations:
        print("无水情数据")
        return

    print(f"\n共找到 {len(all_stations)} 个水情站点:")
    print("=" * 80)

    for i, station in enumerate(all_stations, 1):
        print(f"{i}. 站点信息:")
        print(f"   站名: {station.get('zm', 'N/A')}")
        print(f"   站点编码: {station.get('zh', 'N/A')}")
        print(f"   站点类型: {station.get('station_type_name', 'N/A')}")
        print(f"   站点分类: {station.get('zl', 'N/A')}")
        print(f"   经纬度: {station.get('jd', 'N/A')}, {station.get('wd', 'N/A')}")

        # 显示其他可用字段
        for key, value in station.items():
            if key not in ['zm', 'zh', 'zl', 'jd', 'wd', 'station_type_name'] and value:
                print(f"   {key}: {value}")
        print("-" * 80)

def save_to_json(data, filename=None):
    """保存数据到JSON文件"""
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"water_info_{timestamp}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到: {filename}")
        return filename
    except Exception as e:
        print(f"保存失败: {e}")
        return None

def test_different_areas():
    """测试不同地区的水情获取"""
    test_areas = [
        ('宁波市', '海曙区'),
        ('杭州市', '西湖区'),
        ('温州市', '鹿城区'),
        ('台州市', '椒江区'),
        ('绍兴市', '越城区')
    ]

    print("测试不同地区的水情获取:")
    print("=" * 60)

    for city, district in test_areas:
        print(f"\n测试 {city} {district}:")
        data = get_water_info(city, district)

        if data:
            # 统计所有类型的站点数量
            total_stations = 0
            for key in ['cjj', 'qt', 'cx', 'cbz']:
                if key in data and data[key]:
                    total_stations += len(data[key])

            if total_stations > 0:
                print(f"✅ 成功获取 {total_stations} 个水情站点")
            else:
                print("❌ 该地区无水情站点")
        else:
            print("❌ 获取失败")

        # 添加延迟
        import time
        time.sleep(1)

def main():
    """主函数 - 演示用法"""
    print("浙江省水情信息爬虫 - 简化版")
    print("=" * 60)
    
    # 示例1: 获取宁波市海曙区水情
    print("示例1: 获取宁波市海曙区水情信息")
    data = get_water_info('宁波市', '海曙区')
    
    if data:
        parse_and_display(data)
        save_to_json(data, "ningbo_haishui_water.json")
    
    print("\n" + "=" * 60)
    
    # 示例2: 获取整个宁波市水情 (不指定区县)
    print("示例2: 获取整个宁波市水情信息")
    data = get_water_info('宁波市', '')

    if data:
        # 统计所有类型的站点数量
        total_stations = 0
        for key in ['cjj', 'qt', 'cx', 'cbz']:
            if key in data and data[key]:
                total_stations += len(data[key])

        print(f"宁波市共有 {total_stations} 个水情站点")
        save_to_json(data, "ningbo_all_water.json")
    
    print("\n" + "=" * 60)
    
    # 示例3: 测试多个地区
    test_different_areas()

if __name__ == "__main__":
    main()
